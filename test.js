
(function () {
  "use strict";
  function debounce(func, wait) {
    let timeout;
    return function () {
      let context = this;
      let args = arguments;
      clearTimeout(timeout);
      timeout = setTimeout(function () {
        func.apply(context, args);
      }, wait);
    };
  }

  function exportExcelUi() {
    let centerToolbar = document.querySelector(".center-toolbar");
    if (centerToolbar) {
      let div = centerToolbar.querySelector("div");
      let excelBtn = `
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1706073712360" class="toolbar-copy hope-icon hope-c-XNyZK hope-c-PJLV hope-c-PJLV-ifWVHXq-css" height = "1em" width="1em" viewBox="0 0 1024 1024" version="1.1" p-id="5169">      
              <path d="M169.658182 1024C114.222545 1024 69.818182 978.385455 69.818182 922.717091V101.282909C69.818182 45.614545 114.222545 0 169.658182 0h523.170909l240.034909 246.690909V930.909091a93.090909 93.090909 0 0 1-93.090909 93.090909H169.658182zM653.498182 93.090909H169.658182C166.167273 93.090909 162.909091 96.488727 162.909091 101.282909v821.434182c0 4.794182 3.304727 8.192 6.749091 8.192h670.114909V284.485818L653.544727 93.090909z" fill="#2CB18D" p-id="5170"/>
              <path d="M315.019636 439.016727h79.872L488.727273 579.770182l93.835636-140.753455h79.872l-134.609454 192.698182L671.371636 837.818182h-79.872L488.727273 683.659636 385.954909 837.818182H306.082909l142.429091-206.103273z" fill="#2CB18D" p-id="5171"/>
              <path d="M256 139.636364h372.363636a46.545455 46.545455 0 0 1 46.545455 46.545454v46.545455a46.545455 46.545455 0 0 1-46.545455 46.545454H256a46.545455 46.545455 0 0 1-46.545455-46.545454V186.181818a46.545455 46.545455 0 0 1 46.545455-46.545454z m0 93.090909h372.363636V186.181818H256v46.545455z" fill="#2CB18D" p-id="5172"/>
              <path d="M395.636364 139.636364H349.090909v139.636363h46.545455zM535.272727 139.636364H488.727273v139.636363h46.545454zM233.332364 349.090909h418.909091a23.272727 23.272727 0 1 0 0-46.545454h-418.909091a23.272727 23.272727 0 0 0 0 46.545454z" fill="#2CB18D" p-id="5173"/>
          </svg>
              `;
      let parser = new DOMParser();
      let svgElement = parser.parseFromString(
        excelBtn,
        "image/svg+xml"
      ).documentElement;
      let tooltip = document.createElement("div");
      tooltip.style.display = "none";
      tooltip.style.position = "absolute";
      tooltip.style.background = "#333";
      tooltip.style.color = "#fff";
      tooltip.style.padding = "5px";
      tooltip.style.borderRadius = "5px";
      tooltip.style.fontSize = "13px";
      tooltip.textContent = "导出直链";

      let style = document.createElement("style");
      style.innerHTML = `
          #tooltip::after {
          content: "";
          position: absolute;
          top: 100%;
          left: 50%;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: #333 transparent transparent transparent;
          }
       `;
      document.head.appendChild(style);
      tooltip.id = "tooltip";
      svgElement.addEventListener(
        "mouseover",
        debounce(function (event) {
          let rect = svgElement.getBoundingClientRect();
          tooltip.style.display = "block";
          tooltip.style.left =
            window.scrollX + rect.left + rect.width / 2 - 30 + "px"; // 将 tooltip 定位在元素的中心
          tooltip.style.top = window.scrollY + rect.top - 32 + "px"; // 将 tooltip 定位在元素的上方度，你可以根据需要调整
        }, 10)
      );

      // 添加鼠标离开事件
      svgElement.addEventListener(
        "mouseout",
        debounce(function () {
          tooltip.style.display = "none";
        }, 10)
      );
      let sixthElement = div.children[5];
      if (sixthElement) {
        div.insertBefore(svgElement, sixthElement);
      } else {
        div.appendChild(svgElement);
      }
      document.body.appendChild(tooltip);
      svgElement.addEventListener("click", function () {
        let list = Array.from(document.querySelectorAll(".list-item"));

        let checkedList = list.filter((item) => {
          let checkbox = item.querySelector('input[type="checkbox"]');
          return checkbox && checkbox.checked;
        });
        let result = [];
        for (let item of checkedList) {
          let nameElement = item.querySelector("p.name");
          if (nameElement.textContent.includes(".")) {
            result.push({
              标题: nameElement.textContent,
              直链: item.href.replace(
                window.location.origin,
                `${window.location.origin}/d`
              ),
            });
          } else {
            alert(`${nameElement.textContent} 不是文件，无法导出直链}`);
          }
        }
        if (result.length > 0) {
          let worksheet = XLSX.utils.json_to_sheet(result);
          let workbook = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
          let excelBuffer = XLSX.write(workbook, {
            type: "array",
            bookType: "xlsx",
          });
          let excelBlob = new Blob([excelBuffer], { type: "application/xlsx" });
          saveAs(excelBlob, "直链.xlsx");
          alert("导出成功");
        }
      });
    }
  }
  document.addEventListener("DOMContentLoaded", function () {
    // 创建一个回调函数，该函数在观察到 DOM 变化时执行
    var callback = function (mutationsList, observer) {
      for (let mutation of mutationsList) {
        if (mutation.type === "childList") {
          for (let node of mutation.addedNodes) {
            if (
              node.nodeType === Node.ELEMENT_NODE &&
              node.matches(".center-toolbar")
            ) {
              exportExcelUi();
            }
          }
        }
      }
    };

    // 创建一个观察器实例并传入回调函数
    var observer = new MutationObserver(callback);

    // 配置观察选项:
    var config = { childList: true, subtree: true };

    // 传入目标节点和观察选项
    if (document.body) {
      observer.observe(document.body, config);
    } else {
      console.error("document.body is not available");
    }
  });
})();
</script>

