import { Context, Next } from 'hono';
import { JWTService } from '../utils/auth';
import { CloudflareBindings, JWTPayload } from '../types/interfaces';
import * as response from '../utils/response';

declare module 'hono' {
  interface ContextVariableMap {
    user: JWTPayload;
  }
}

export function authMiddleware() {
  return async (c: Context<{ Bindings: CloudflareBindings }>, next: Next) => {
    const authHeader = c.req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return response.error(c, '未授权访问：缺少或无效的token',  401);
    }

    const token = authHeader.substring(7);
    const jwtService = new JWTService(c.env.JWT_SECRET);

    try {
      const payload = await jwtService.verifyToken(token);
      
      // 检查token是否过期
      if (payload.exp < Math.floor(Date.now() / 1000)) {
        return response.error(c, '未授权访问：token已过期',  401);
      }

      c.set('user', payload);
      await next();
    } catch (err) {
      return response.error(c, '未授权访问：无效的token',  401);
    }
  };
}

export function superAdminOnly() {
  return async (c: Context<{ Bindings: CloudflareBindings }>, next: Next) => {
    const user = c.get('user');
    
    if (!user || user.role !== 'admin') {
      return response.error(c, '权限不足：需要超级管理员权限',  403);
    }

    await next();
  };
}

export function adminOnly() {
  return async (c: Context<{ Bindings: CloudflareBindings }>, next: Next) => {
    const user = c.get('user');
    
    if (!user || user.role !== 'admin') {
      return response.error(c, '权限不足：需要管理员权限',  403);
    }

    await next();
  };
}

// 检查普通管理员是否有权限操作指定产品
export function checkProductAccess(productIdParam?: string) {
  return async (c: Context<{ Bindings: CloudflareBindings }>, next: Next) => {
    const admin = c.get('admin');
    
    // 超级管理员跳过检查
    if (admin.role === 'super') {
      await next();
      return;
    }

    // 获取产品ID
    let productId = productIdParam;
    if (!productId) {
      // 从路径参数或请求体中获取product_id
      productId = c.req.param('product_id') || c.req.param('id');
      if (!productId) {
        try {
          const body = await c.req.json();
          productId = body.product_id;
        } catch {
          // 忽略解析错误
        }
      }
    }

    if (productId) {
      // 从缓存中获取管理员权限信息
      const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);
      if (adminInfo) {
        const { product_ids } = JSON.parse(adminInfo);
        if (!product_ids || !product_ids.includes(productId.toString())) {
          return response.error(c, '权限不足：无权限操作该产品',  403);
        }
      } else {
        // 缓存未命中，从数据库查询
        const adminRecord = await c.env.DB.prepare('SELECT product_ids FROM admins WHERE id = ?').bind(admin.admin_id).first();
        if (adminRecord && adminRecord.product_ids) {
          const productIds = JSON.parse(adminRecord.product_ids as string);
          if (!productIds.includes(parseInt(productId))) {
            return response.error(c, '权限不足：无权限操作该产品', 403);
          }
        } else {
          return response.error(c, '权限不足：无权限操作该产品', 403);
        }
      }
    }

    await next();
  };
}