{"name": "backend", "type": "module", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings"}, "dependencies": {"crypto-js": "^4.2.0", "hono": "^4.8.10", "zod": "^4.0.5"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250731.0", "@types/crypto-js": "^4.2.2", "bcryptjs": "^3.0.2", "typescript": "^5.8.3", "wrangler": "^4.4.0"}}