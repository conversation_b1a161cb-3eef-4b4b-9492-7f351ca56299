var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// wrangler-modules-watch:wrangler:modules-watch
var init_wrangler_modules_watch = __esm({
  "wrangler-modules-watch:wrangler:modules-watch"() {
    init_modules_watch_stub();
  }
});

// node_modules/.pnpm/wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0/node_modules/wrangler/templates/modules-watch-stub.js
var init_modules_watch_stub = __esm({
  "node_modules/.pnpm/wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0/node_modules/wrangler/templates/modules-watch-stub.js"() {
    init_wrangler_modules_watch();
  }
});

// (disabled):crypto
var require_crypto = __commonJS({
  "(disabled):crypto"() {
    init_modules_watch_stub();
  }
});

// .wrangler/tmp/bundle-i513Wd/middleware-loader.entry.ts
init_modules_watch_stub();

// .wrangler/tmp/bundle-i513Wd/middleware-insertion-facade.js
init_modules_watch_stub();

// src/index.ts
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono-base.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/compose.js
init_modules_watch_stub();
var compose = /* @__PURE__ */ __name((middleware, onError, onNotFound) => {
  return (context, next) => {
    let index = -1;
    return dispatch(0);
    async function dispatch(i) {
      if (i <= index) {
        throw new Error("next() called multiple times");
      }
      index = i;
      let res;
      let isError = false;
      let handler;
      if (middleware[i]) {
        handler = middleware[i][0][0];
        context.req.routeIndex = i;
      } else {
        handler = i === middleware.length && next || void 0;
      }
      if (handler) {
        try {
          res = await handler(context, () => dispatch(i + 1));
        } catch (err) {
          if (err instanceof Error && onError) {
            context.error = err;
            res = await onError(err, context);
            isError = true;
          } else {
            throw err;
          }
        }
      } else {
        if (context.finalized === false && onNotFound) {
          res = await onNotFound(context);
        }
      }
      if (res && (context.finalized === false || isError)) {
        context.res = res;
      }
      return context;
    }
    __name(dispatch, "dispatch");
  };
}, "compose");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/context.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/request.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/request/constants.js
init_modules_watch_stub();
var GET_MATCH_RESULT = Symbol();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/body.js
init_modules_watch_stub();
var parseBody = /* @__PURE__ */ __name(async (request, options = /* @__PURE__ */ Object.create(null)) => {
  const { all = false, dot = false } = options;
  const headers = request instanceof HonoRequest ? request.raw.headers : request.headers;
  const contentType = headers.get("Content-Type");
  if (contentType?.startsWith("multipart/form-data") || contentType?.startsWith("application/x-www-form-urlencoded")) {
    return parseFormData(request, { all, dot });
  }
  return {};
}, "parseBody");
async function parseFormData(request, options) {
  const formData = await request.formData();
  if (formData) {
    return convertFormDataToBodyData(formData, options);
  }
  return {};
}
__name(parseFormData, "parseFormData");
function convertFormDataToBodyData(formData, options) {
  const form = /* @__PURE__ */ Object.create(null);
  formData.forEach((value, key) => {
    const shouldParseAllValues = options.all || key.endsWith("[]");
    if (!shouldParseAllValues) {
      form[key] = value;
    } else {
      handleParsingAllValues(form, key, value);
    }
  });
  if (options.dot) {
    Object.entries(form).forEach(([key, value]) => {
      const shouldParseDotValues = key.includes(".");
      if (shouldParseDotValues) {
        handleParsingNestedValues(form, key, value);
        delete form[key];
      }
    });
  }
  return form;
}
__name(convertFormDataToBodyData, "convertFormDataToBodyData");
var handleParsingAllValues = /* @__PURE__ */ __name((form, key, value) => {
  if (form[key] !== void 0) {
    if (Array.isArray(form[key])) {
      ;
      form[key].push(value);
    } else {
      form[key] = [form[key], value];
    }
  } else {
    if (!key.endsWith("[]")) {
      form[key] = value;
    } else {
      form[key] = [value];
    }
  }
}, "handleParsingAllValues");
var handleParsingNestedValues = /* @__PURE__ */ __name((form, key, value) => {
  let nestedForm = form;
  const keys = key.split(".");
  keys.forEach((key2, index) => {
    if (index === keys.length - 1) {
      nestedForm[key2] = value;
    } else {
      if (!nestedForm[key2] || typeof nestedForm[key2] !== "object" || Array.isArray(nestedForm[key2]) || nestedForm[key2] instanceof File) {
        nestedForm[key2] = /* @__PURE__ */ Object.create(null);
      }
      nestedForm = nestedForm[key2];
    }
  });
}, "handleParsingNestedValues");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/url.js
init_modules_watch_stub();
var splitPath = /* @__PURE__ */ __name((path) => {
  const paths = path.split("/");
  if (paths[0] === "") {
    paths.shift();
  }
  return paths;
}, "splitPath");
var splitRoutingPath = /* @__PURE__ */ __name((routePath) => {
  const { groups, path } = extractGroupsFromPath(routePath);
  const paths = splitPath(path);
  return replaceGroupMarks(paths, groups);
}, "splitRoutingPath");
var extractGroupsFromPath = /* @__PURE__ */ __name((path) => {
  const groups = [];
  path = path.replace(/\{[^}]+\}/g, (match, index) => {
    const mark = `@${index}`;
    groups.push([mark, match]);
    return mark;
  });
  return { groups, path };
}, "extractGroupsFromPath");
var replaceGroupMarks = /* @__PURE__ */ __name((paths, groups) => {
  for (let i = groups.length - 1; i >= 0; i--) {
    const [mark] = groups[i];
    for (let j = paths.length - 1; j >= 0; j--) {
      if (paths[j].includes(mark)) {
        paths[j] = paths[j].replace(mark, groups[i][1]);
        break;
      }
    }
  }
  return paths;
}, "replaceGroupMarks");
var patternCache = {};
var getPattern = /* @__PURE__ */ __name((label, next) => {
  if (label === "*") {
    return "*";
  }
  const match = label.match(/^\:([^\{\}]+)(?:\{(.+)\})?$/);
  if (match) {
    const cacheKey = `${label}#${next}`;
    if (!patternCache[cacheKey]) {
      if (match[2]) {
        patternCache[cacheKey] = next && next[0] !== ":" && next[0] !== "*" ? [cacheKey, match[1], new RegExp(`^${match[2]}(?=/${next})`)] : [label, match[1], new RegExp(`^${match[2]}$`)];
      } else {
        patternCache[cacheKey] = [label, match[1], true];
      }
    }
    return patternCache[cacheKey];
  }
  return null;
}, "getPattern");
var tryDecode = /* @__PURE__ */ __name((str, decoder) => {
  try {
    return decoder(str);
  } catch {
    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {
      try {
        return decoder(match);
      } catch {
        return match;
      }
    });
  }
}, "tryDecode");
var tryDecodeURI = /* @__PURE__ */ __name((str) => tryDecode(str, decodeURI), "tryDecodeURI");
var getPath = /* @__PURE__ */ __name((request) => {
  const url = request.url;
  const start = url.indexOf(
    "/",
    url.charCodeAt(9) === 58 ? 13 : 8
  );
  let i = start;
  for (; i < url.length; i++) {
    const charCode = url.charCodeAt(i);
    if (charCode === 37) {
      const queryIndex = url.indexOf("?", i);
      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);
      return tryDecodeURI(path.includes("%25") ? path.replace(/%25/g, "%2525") : path);
    } else if (charCode === 63) {
      break;
    }
  }
  return url.slice(start, i);
}, "getPath");
var getPathNoStrict = /* @__PURE__ */ __name((request) => {
  const result = getPath(request);
  return result.length > 1 && result.at(-1) === "/" ? result.slice(0, -1) : result;
}, "getPathNoStrict");
var mergePath = /* @__PURE__ */ __name((base, sub, ...rest) => {
  if (rest.length) {
    sub = mergePath(sub, ...rest);
  }
  return `${base?.[0] === "/" ? "" : "/"}${base}${sub === "/" ? "" : `${base?.at(-1) === "/" ? "" : "/"}${sub?.[0] === "/" ? sub.slice(1) : sub}`}`;
}, "mergePath");
var checkOptionalParameter = /* @__PURE__ */ __name((path) => {
  if (path.charCodeAt(path.length - 1) !== 63 || !path.includes(":")) {
    return null;
  }
  const segments = path.split("/");
  const results = [];
  let basePath = "";
  segments.forEach((segment) => {
    if (segment !== "" && !/\:/.test(segment)) {
      basePath += "/" + segment;
    } else if (/\:/.test(segment)) {
      if (/\?/.test(segment)) {
        if (results.length === 0 && basePath === "") {
          results.push("/");
        } else {
          results.push(basePath);
        }
        const optionalSegment = segment.replace("?", "");
        basePath += "/" + optionalSegment;
        results.push(basePath);
      } else {
        basePath += "/" + segment;
      }
    }
  });
  return results.filter((v, i, a) => a.indexOf(v) === i);
}, "checkOptionalParameter");
var _decodeURI = /* @__PURE__ */ __name((value) => {
  if (!/[%+]/.test(value)) {
    return value;
  }
  if (value.indexOf("+") !== -1) {
    value = value.replace(/\+/g, " ");
  }
  return value.indexOf("%") !== -1 ? tryDecode(value, decodeURIComponent_) : value;
}, "_decodeURI");
var _getQueryParam = /* @__PURE__ */ __name((url, key, multiple) => {
  let encoded;
  if (!multiple && key && !/[%+]/.test(key)) {
    let keyIndex2 = url.indexOf(`?${key}`, 8);
    if (keyIndex2 === -1) {
      keyIndex2 = url.indexOf(`&${key}`, 8);
    }
    while (keyIndex2 !== -1) {
      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);
      if (trailingKeyCode === 61) {
        const valueIndex = keyIndex2 + key.length + 2;
        const endIndex = url.indexOf("&", valueIndex);
        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));
      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {
        return "";
      }
      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);
    }
    encoded = /[%+]/.test(url);
    if (!encoded) {
      return void 0;
    }
  }
  const results = {};
  encoded ??= /[%+]/.test(url);
  let keyIndex = url.indexOf("?", 8);
  while (keyIndex !== -1) {
    const nextKeyIndex = url.indexOf("&", keyIndex + 1);
    let valueIndex = url.indexOf("=", keyIndex);
    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {
      valueIndex = -1;
    }
    let name = url.slice(
      keyIndex + 1,
      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex
    );
    if (encoded) {
      name = _decodeURI(name);
    }
    keyIndex = nextKeyIndex;
    if (name === "") {
      continue;
    }
    let value;
    if (valueIndex === -1) {
      value = "";
    } else {
      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);
      if (encoded) {
        value = _decodeURI(value);
      }
    }
    if (multiple) {
      if (!(results[name] && Array.isArray(results[name]))) {
        results[name] = [];
      }
      ;
      results[name].push(value);
    } else {
      results[name] ??= value;
    }
  }
  return key ? results[key] : results;
}, "_getQueryParam");
var getQueryParam = _getQueryParam;
var getQueryParams = /* @__PURE__ */ __name((url, key) => {
  return _getQueryParam(url, key, true);
}, "getQueryParams");
var decodeURIComponent_ = decodeURIComponent;

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/request.js
var tryDecodeURIComponent = /* @__PURE__ */ __name((str) => tryDecode(str, decodeURIComponent_), "tryDecodeURIComponent");
var HonoRequest = class {
  static {
    __name(this, "HonoRequest");
  }
  raw;
  #validatedData;
  #matchResult;
  routeIndex = 0;
  path;
  bodyCache = {};
  constructor(request, path = "/", matchResult = [[]]) {
    this.raw = request;
    this.path = path;
    this.#matchResult = matchResult;
    this.#validatedData = {};
  }
  param(key) {
    return key ? this.#getDecodedParam(key) : this.#getAllDecodedParams();
  }
  #getDecodedParam(key) {
    const paramKey = this.#matchResult[0][this.routeIndex][1][key];
    const param = this.#getParamValue(paramKey);
    return param ? /\%/.test(param) ? tryDecodeURIComponent(param) : param : void 0;
  }
  #getAllDecodedParams() {
    const decoded = {};
    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);
    for (const key of keys) {
      const value = this.#getParamValue(this.#matchResult[0][this.routeIndex][1][key]);
      if (value && typeof value === "string") {
        decoded[key] = /\%/.test(value) ? tryDecodeURIComponent(value) : value;
      }
    }
    return decoded;
  }
  #getParamValue(paramKey) {
    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;
  }
  query(key) {
    return getQueryParam(this.url, key);
  }
  queries(key) {
    return getQueryParams(this.url, key);
  }
  header(name) {
    if (name) {
      return this.raw.headers.get(name) ?? void 0;
    }
    const headerData = {};
    this.raw.headers.forEach((value, key) => {
      headerData[key] = value;
    });
    return headerData;
  }
  async parseBody(options) {
    return this.bodyCache.parsedBody ??= await parseBody(this, options);
  }
  #cachedBody = /* @__PURE__ */ __name((key) => {
    const { bodyCache, raw: raw2 } = this;
    const cachedBody = bodyCache[key];
    if (cachedBody) {
      return cachedBody;
    }
    const anyCachedKey = Object.keys(bodyCache)[0];
    if (anyCachedKey) {
      return bodyCache[anyCachedKey].then((body) => {
        if (anyCachedKey === "json") {
          body = JSON.stringify(body);
        }
        return new Response(body)[key]();
      });
    }
    return bodyCache[key] = raw2[key]();
  }, "#cachedBody");
  json() {
    return this.#cachedBody("text").then((text) => JSON.parse(text));
  }
  text() {
    return this.#cachedBody("text");
  }
  arrayBuffer() {
    return this.#cachedBody("arrayBuffer");
  }
  blob() {
    return this.#cachedBody("blob");
  }
  formData() {
    return this.#cachedBody("formData");
  }
  addValidatedData(target, data) {
    this.#validatedData[target] = data;
  }
  valid(target) {
    return this.#validatedData[target];
  }
  get url() {
    return this.raw.url;
  }
  get method() {
    return this.raw.method;
  }
  get [GET_MATCH_RESULT]() {
    return this.#matchResult;
  }
  get matchedRoutes() {
    return this.#matchResult[0].map(([[, route]]) => route);
  }
  get routePath() {
    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/html.js
init_modules_watch_stub();
var HtmlEscapedCallbackPhase = {
  Stringify: 1,
  BeforeStream: 2,
  Stream: 3
};
var raw = /* @__PURE__ */ __name((value, callbacks) => {
  const escapedString = new String(value);
  escapedString.isEscaped = true;
  escapedString.callbacks = callbacks;
  return escapedString;
}, "raw");
var resolveCallback = /* @__PURE__ */ __name(async (str, phase, preserveCallbacks, context, buffer) => {
  if (typeof str === "object" && !(str instanceof String)) {
    if (!(str instanceof Promise)) {
      str = str.toString();
    }
    if (str instanceof Promise) {
      str = await str;
    }
  }
  const callbacks = str.callbacks;
  if (!callbacks?.length) {
    return Promise.resolve(str);
  }
  if (buffer) {
    buffer[0] += str;
  } else {
    buffer = [str];
  }
  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(
    (res) => Promise.all(
      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))
    ).then(() => buffer[0])
  );
  if (preserveCallbacks) {
    return raw(await resStr, callbacks);
  } else {
    return resStr;
  }
}, "resolveCallback");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/context.js
var TEXT_PLAIN = "text/plain; charset=UTF-8";
var setDefaultContentType = /* @__PURE__ */ __name((contentType, headers) => {
  return {
    "Content-Type": contentType,
    ...headers
  };
}, "setDefaultContentType");
var Context = class {
  static {
    __name(this, "Context");
  }
  #rawRequest;
  #req;
  env = {};
  #var;
  finalized = false;
  error;
  #status;
  #executionCtx;
  #res;
  #layout;
  #renderer;
  #notFoundHandler;
  #preparedHeaders;
  #matchResult;
  #path;
  constructor(req, options) {
    this.#rawRequest = req;
    if (options) {
      this.#executionCtx = options.executionCtx;
      this.env = options.env;
      this.#notFoundHandler = options.notFoundHandler;
      this.#path = options.path;
      this.#matchResult = options.matchResult;
    }
  }
  get req() {
    this.#req ??= new HonoRequest(this.#rawRequest, this.#path, this.#matchResult);
    return this.#req;
  }
  get event() {
    if (this.#executionCtx && "respondWith" in this.#executionCtx) {
      return this.#executionCtx;
    } else {
      throw Error("This context has no FetchEvent");
    }
  }
  get executionCtx() {
    if (this.#executionCtx) {
      return this.#executionCtx;
    } else {
      throw Error("This context has no ExecutionContext");
    }
  }
  get res() {
    return this.#res ||= new Response(null, {
      headers: this.#preparedHeaders ??= new Headers()
    });
  }
  set res(_res) {
    if (this.#res && _res) {
      _res = new Response(_res.body, _res);
      for (const [k, v] of this.#res.headers.entries()) {
        if (k === "content-type") {
          continue;
        }
        if (k === "set-cookie") {
          const cookies = this.#res.headers.getSetCookie();
          _res.headers.delete("set-cookie");
          for (const cookie of cookies) {
            _res.headers.append("set-cookie", cookie);
          }
        } else {
          _res.headers.set(k, v);
        }
      }
    }
    this.#res = _res;
    this.finalized = true;
  }
  render = /* @__PURE__ */ __name((...args) => {
    this.#renderer ??= (content) => this.html(content);
    return this.#renderer(...args);
  }, "render");
  setLayout = /* @__PURE__ */ __name((layout) => this.#layout = layout, "setLayout");
  getLayout = /* @__PURE__ */ __name(() => this.#layout, "getLayout");
  setRenderer = /* @__PURE__ */ __name((renderer) => {
    this.#renderer = renderer;
  }, "setRenderer");
  header = /* @__PURE__ */ __name((name, value, options) => {
    if (this.finalized) {
      this.#res = new Response(this.#res.body, this.#res);
    }
    const headers = this.#res ? this.#res.headers : this.#preparedHeaders ??= new Headers();
    if (value === void 0) {
      headers.delete(name);
    } else if (options?.append) {
      headers.append(name, value);
    } else {
      headers.set(name, value);
    }
  }, "header");
  status = /* @__PURE__ */ __name((status) => {
    this.#status = status;
  }, "status");
  set = /* @__PURE__ */ __name((key, value) => {
    this.#var ??= /* @__PURE__ */ new Map();
    this.#var.set(key, value);
  }, "set");
  get = /* @__PURE__ */ __name((key) => {
    return this.#var ? this.#var.get(key) : void 0;
  }, "get");
  get var() {
    if (!this.#var) {
      return {};
    }
    return Object.fromEntries(this.#var);
  }
  #newResponse(data, arg, headers) {
    const responseHeaders = this.#res ? new Headers(this.#res.headers) : this.#preparedHeaders ?? new Headers();
    if (typeof arg === "object" && "headers" in arg) {
      const argHeaders = arg.headers instanceof Headers ? arg.headers : new Headers(arg.headers);
      for (const [key, value] of argHeaders) {
        if (key.toLowerCase() === "set-cookie") {
          responseHeaders.append(key, value);
        } else {
          responseHeaders.set(key, value);
        }
      }
    }
    if (headers) {
      for (const [k, v] of Object.entries(headers)) {
        if (typeof v === "string") {
          responseHeaders.set(k, v);
        } else {
          responseHeaders.delete(k);
          for (const v2 of v) {
            responseHeaders.append(k, v2);
          }
        }
      }
    }
    const status = typeof arg === "number" ? arg : arg?.status ?? this.#status;
    return new Response(data, { status, headers: responseHeaders });
  }
  newResponse = /* @__PURE__ */ __name((...args) => this.#newResponse(...args), "newResponse");
  body = /* @__PURE__ */ __name((data, arg, headers) => this.#newResponse(data, arg, headers), "body");
  text = /* @__PURE__ */ __name((text, arg, headers) => {
    return !this.#preparedHeaders && !this.#status && !arg && !headers && !this.finalized ? new Response(text) : this.#newResponse(
      text,
      arg,
      setDefaultContentType(TEXT_PLAIN, headers)
    );
  }, "text");
  json = /* @__PURE__ */ __name((object, arg, headers) => {
    return this.#newResponse(
      JSON.stringify(object),
      arg,
      setDefaultContentType("application/json", headers)
    );
  }, "json");
  html = /* @__PURE__ */ __name((html, arg, headers) => {
    const res = /* @__PURE__ */ __name((html2) => this.#newResponse(html2, arg, setDefaultContentType("text/html; charset=UTF-8", headers)), "res");
    return typeof html === "object" ? resolveCallback(html, HtmlEscapedCallbackPhase.Stringify, false, {}).then(res) : res(html);
  }, "html");
  redirect = /* @__PURE__ */ __name((location, status) => {
    const locationString = String(location);
    this.header(
      "Location",
      !/[^\x00-\xFF]/.test(locationString) ? locationString : encodeURI(locationString)
    );
    return this.newResponse(null, status ?? 302);
  }, "redirect");
  notFound = /* @__PURE__ */ __name(() => {
    this.#notFoundHandler ??= () => new Response();
    return this.#notFoundHandler(this);
  }, "notFound");
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router.js
init_modules_watch_stub();
var METHOD_NAME_ALL = "ALL";
var METHOD_NAME_ALL_LOWERCASE = "all";
var METHODS = ["get", "post", "put", "delete", "options", "patch"];
var MESSAGE_MATCHER_IS_ALREADY_BUILT = "Can not add a route since the matcher is already built.";
var UnsupportedPathError = class extends Error {
  static {
    __name(this, "UnsupportedPathError");
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/constants.js
init_modules_watch_stub();
var COMPOSED_HANDLER = "__COMPOSED_HANDLER";

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono-base.js
var notFoundHandler = /* @__PURE__ */ __name((c) => {
  return c.text("404 Not Found", 404);
}, "notFoundHandler");
var errorHandler = /* @__PURE__ */ __name((err, c) => {
  if ("getResponse" in err) {
    const res = err.getResponse();
    return c.newResponse(res.body, res);
  }
  console.error(err);
  return c.text("Internal Server Error", 500);
}, "errorHandler");
var Hono = class {
  static {
    __name(this, "Hono");
  }
  get;
  post;
  put;
  delete;
  options;
  patch;
  all;
  on;
  use;
  router;
  getPath;
  _basePath = "/";
  #path = "/";
  routes = [];
  constructor(options = {}) {
    const allMethods = [...METHODS, METHOD_NAME_ALL_LOWERCASE];
    allMethods.forEach((method) => {
      this[method] = (args1, ...args) => {
        if (typeof args1 === "string") {
          this.#path = args1;
        } else {
          this.#addRoute(method, this.#path, args1);
        }
        args.forEach((handler) => {
          this.#addRoute(method, this.#path, handler);
        });
        return this;
      };
    });
    this.on = (method, path, ...handlers) => {
      for (const p of [path].flat()) {
        this.#path = p;
        for (const m of [method].flat()) {
          handlers.map((handler) => {
            this.#addRoute(m.toUpperCase(), this.#path, handler);
          });
        }
      }
      return this;
    };
    this.use = (arg1, ...handlers) => {
      if (typeof arg1 === "string") {
        this.#path = arg1;
      } else {
        this.#path = "*";
        handlers.unshift(arg1);
      }
      handlers.forEach((handler) => {
        this.#addRoute(METHOD_NAME_ALL, this.#path, handler);
      });
      return this;
    };
    const { strict, ...optionsWithoutStrict } = options;
    Object.assign(this, optionsWithoutStrict);
    this.getPath = strict ?? true ? options.getPath ?? getPath : getPathNoStrict;
  }
  #clone() {
    const clone = new Hono({
      router: this.router,
      getPath: this.getPath
    });
    clone.errorHandler = this.errorHandler;
    clone.#notFoundHandler = this.#notFoundHandler;
    clone.routes = this.routes;
    return clone;
  }
  #notFoundHandler = notFoundHandler;
  errorHandler = errorHandler;
  route(path, app2) {
    const subApp = this.basePath(path);
    app2.routes.map((r) => {
      let handler;
      if (app2.errorHandler === errorHandler) {
        handler = r.handler;
      } else {
        handler = /* @__PURE__ */ __name(async (c, next) => (await compose([], app2.errorHandler)(c, () => r.handler(c, next))).res, "handler");
        handler[COMPOSED_HANDLER] = r.handler;
      }
      subApp.#addRoute(r.method, r.path, handler);
    });
    return this;
  }
  basePath(path) {
    const subApp = this.#clone();
    subApp._basePath = mergePath(this._basePath, path);
    return subApp;
  }
  onError = /* @__PURE__ */ __name((handler) => {
    this.errorHandler = handler;
    return this;
  }, "onError");
  notFound = /* @__PURE__ */ __name((handler) => {
    this.#notFoundHandler = handler;
    return this;
  }, "notFound");
  mount(path, applicationHandler, options) {
    let replaceRequest;
    let optionHandler;
    if (options) {
      if (typeof options === "function") {
        optionHandler = options;
      } else {
        optionHandler = options.optionHandler;
        if (options.replaceRequest === false) {
          replaceRequest = /* @__PURE__ */ __name((request) => request, "replaceRequest");
        } else {
          replaceRequest = options.replaceRequest;
        }
      }
    }
    const getOptions = optionHandler ? (c) => {
      const options2 = optionHandler(c);
      return Array.isArray(options2) ? options2 : [options2];
    } : (c) => {
      let executionContext = void 0;
      try {
        executionContext = c.executionCtx;
      } catch {
      }
      return [c.env, executionContext];
    };
    replaceRequest ||= (() => {
      const mergedPath = mergePath(this._basePath, path);
      const pathPrefixLength = mergedPath === "/" ? 0 : mergedPath.length;
      return (request) => {
        const url = new URL(request.url);
        url.pathname = url.pathname.slice(pathPrefixLength) || "/";
        return new Request(url, request);
      };
    })();
    const handler = /* @__PURE__ */ __name(async (c, next) => {
      const res = await applicationHandler(replaceRequest(c.req.raw), ...getOptions(c));
      if (res) {
        return res;
      }
      await next();
    }, "handler");
    this.#addRoute(METHOD_NAME_ALL, mergePath(path, "*"), handler);
    return this;
  }
  #addRoute(method, path, handler) {
    method = method.toUpperCase();
    path = mergePath(this._basePath, path);
    const r = { basePath: this._basePath, path, method, handler };
    this.router.add(method, path, [handler, r]);
    this.routes.push(r);
  }
  #handleError(err, c) {
    if (err instanceof Error) {
      return this.errorHandler(err, c);
    }
    throw err;
  }
  #dispatch(request, executionCtx, env, method) {
    if (method === "HEAD") {
      return (async () => new Response(null, await this.#dispatch(request, executionCtx, env, "GET")))();
    }
    const path = this.getPath(request, { env });
    const matchResult = this.router.match(method, path);
    const c = new Context(request, {
      path,
      matchResult,
      env,
      executionCtx,
      notFoundHandler: this.#notFoundHandler
    });
    if (matchResult[0].length === 1) {
      let res;
      try {
        res = matchResult[0][0][0][0](c, async () => {
          c.res = await this.#notFoundHandler(c);
        });
      } catch (err) {
        return this.#handleError(err, c);
      }
      return res instanceof Promise ? res.then(
        (resolved) => resolved || (c.finalized ? c.res : this.#notFoundHandler(c))
      ).catch((err) => this.#handleError(err, c)) : res ?? this.#notFoundHandler(c);
    }
    const composed = compose(matchResult[0], this.errorHandler, this.#notFoundHandler);
    return (async () => {
      try {
        const context = await composed(c);
        if (!context.finalized) {
          throw new Error(
            "Context is not finalized. Did you forget to return a Response object or `await next()`?"
          );
        }
        return context.res;
      } catch (err) {
        return this.#handleError(err, c);
      }
    })();
  }
  fetch = /* @__PURE__ */ __name((request, ...rest) => {
    return this.#dispatch(request, rest[1], rest[0], request.method);
  }, "fetch");
  request = /* @__PURE__ */ __name((input, requestInit, Env, executionCtx) => {
    if (input instanceof Request) {
      return this.fetch(requestInit ? new Request(input, requestInit) : input, Env, executionCtx);
    }
    input = input.toString();
    return this.fetch(
      new Request(
        /^https?:\/\//.test(input) ? input : `http://localhost${mergePath("/", input)}`,
        requestInit
      ),
      Env,
      executionCtx
    );
  }, "request");
  fire = /* @__PURE__ */ __name(() => {
    addEventListener("fetch", (event) => {
      event.respondWith(this.#dispatch(event.request, event, void 0, event.request.method));
    });
  }, "fire");
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/router.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/node.js
init_modules_watch_stub();
var LABEL_REG_EXP_STR = "[^/]+";
var ONLY_WILDCARD_REG_EXP_STR = ".*";
var TAIL_WILDCARD_REG_EXP_STR = "(?:|/.*)";
var PATH_ERROR = Symbol();
var regExpMetaChars = new Set(".\\+*[^]$()");
function compareKey(a, b) {
  if (a.length === 1) {
    return b.length === 1 ? a < b ? -1 : 1 : -1;
  }
  if (b.length === 1) {
    return 1;
  }
  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {
    return 1;
  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {
    return -1;
  }
  if (a === LABEL_REG_EXP_STR) {
    return 1;
  } else if (b === LABEL_REG_EXP_STR) {
    return -1;
  }
  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;
}
__name(compareKey, "compareKey");
var Node = class {
  static {
    __name(this, "Node");
  }
  #index;
  #varIndex;
  #children = /* @__PURE__ */ Object.create(null);
  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {
    if (tokens.length === 0) {
      if (this.#index !== void 0) {
        throw PATH_ERROR;
      }
      if (pathErrorCheckOnly) {
        return;
      }
      this.#index = index;
      return;
    }
    const [token, ...restTokens] = tokens;
    const pattern = token === "*" ? restTokens.length === 0 ? ["", "", ONLY_WILDCARD_REG_EXP_STR] : ["", "", LABEL_REG_EXP_STR] : token === "/*" ? ["", "", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\:([^\{\}]+)(?:\{(.+)\})?$/);
    let node;
    if (pattern) {
      const name = pattern[1];
      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;
      if (name && pattern[2]) {
        regexpStr = regexpStr.replace(/^\((?!\?:)(?=[^)]+\)$)/, "(?:");
        if (/\((?!\?:)/.test(regexpStr)) {
          throw PATH_ERROR;
        }
      }
      node = this.#children[regexpStr];
      if (!node) {
        if (Object.keys(this.#children).some(
          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR
        )) {
          throw PATH_ERROR;
        }
        if (pathErrorCheckOnly) {
          return;
        }
        node = this.#children[regexpStr] = new Node();
        if (name !== "") {
          node.#varIndex = context.varIndex++;
        }
      }
      if (!pathErrorCheckOnly && name !== "") {
        paramMap.push([name, node.#varIndex]);
      }
    } else {
      node = this.#children[token];
      if (!node) {
        if (Object.keys(this.#children).some(
          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR
        )) {
          throw PATH_ERROR;
        }
        if (pathErrorCheckOnly) {
          return;
        }
        node = this.#children[token] = new Node();
      }
    }
    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);
  }
  buildRegExpStr() {
    const childKeys = Object.keys(this.#children).sort(compareKey);
    const strList = childKeys.map((k) => {
      const c = this.#children[k];
      return (typeof c.#varIndex === "number" ? `(${k})@${c.#varIndex}` : regExpMetaChars.has(k) ? `\\${k}` : k) + c.buildRegExpStr();
    });
    if (typeof this.#index === "number") {
      strList.unshift(`#${this.#index}`);
    }
    if (strList.length === 0) {
      return "";
    }
    if (strList.length === 1) {
      return strList[0];
    }
    return "(?:" + strList.join("|") + ")";
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/trie.js
init_modules_watch_stub();
var Trie = class {
  static {
    __name(this, "Trie");
  }
  #context = { varIndex: 0 };
  #root = new Node();
  insert(path, index, pathErrorCheckOnly) {
    const paramAssoc = [];
    const groups = [];
    for (let i = 0; ; ) {
      let replaced = false;
      path = path.replace(/\{[^}]+\}/g, (m) => {
        const mark = `@\\${i}`;
        groups[i] = [mark, m];
        i++;
        replaced = true;
        return mark;
      });
      if (!replaced) {
        break;
      }
    }
    const tokens = path.match(/(?::[^\/]+)|(?:\/\*$)|./g) || [];
    for (let i = groups.length - 1; i >= 0; i--) {
      const [mark] = groups[i];
      for (let j = tokens.length - 1; j >= 0; j--) {
        if (tokens[j].indexOf(mark) !== -1) {
          tokens[j] = tokens[j].replace(mark, groups[i][1]);
          break;
        }
      }
    }
    this.#root.insert(tokens, index, paramAssoc, this.#context, pathErrorCheckOnly);
    return paramAssoc;
  }
  buildRegExp() {
    let regexp = this.#root.buildRegExpStr();
    if (regexp === "") {
      return [/^$/, [], []];
    }
    let captureIndex = 0;
    const indexReplacementMap = [];
    const paramReplacementMap = [];
    regexp = regexp.replace(/#(\d+)|@(\d+)|\.\*\$/g, (_, handlerIndex, paramIndex) => {
      if (handlerIndex !== void 0) {
        indexReplacementMap[++captureIndex] = Number(handlerIndex);
        return "$()";
      }
      if (paramIndex !== void 0) {
        paramReplacementMap[Number(paramIndex)] = ++captureIndex;
        return "";
      }
      return "";
    });
    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/router.js
var emptyParam = [];
var nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];
var wildcardRegExpCache = /* @__PURE__ */ Object.create(null);
function buildWildcardRegExp(path) {
  return wildcardRegExpCache[path] ??= new RegExp(
    path === "*" ? "" : `^${path.replace(
      /\/\*$|([.\\+*[^\]$()])/g,
      (_, metaChar) => metaChar ? `\\${metaChar}` : "(?:|/.*)"
    )}$`
  );
}
__name(buildWildcardRegExp, "buildWildcardRegExp");
function clearWildcardRegExpCache() {
  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);
}
__name(clearWildcardRegExpCache, "clearWildcardRegExpCache");
function buildMatcherFromPreprocessedRoutes(routes) {
  const trie = new Trie();
  const handlerData = [];
  if (routes.length === 0) {
    return nullMatcher;
  }
  const routesWithStaticPathFlag = routes.map(
    (route) => [!/\*|\/:/.test(route[0]), ...route]
  ).sort(
    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length
  );
  const staticMap = /* @__PURE__ */ Object.create(null);
  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {
    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];
    if (pathErrorCheckOnly) {
      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];
    } else {
      j++;
    }
    let paramAssoc;
    try {
      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);
    } catch (e) {
      throw e === PATH_ERROR ? new UnsupportedPathError(path) : e;
    }
    if (pathErrorCheckOnly) {
      continue;
    }
    handlerData[j] = handlers.map(([h, paramCount]) => {
      const paramIndexMap = /* @__PURE__ */ Object.create(null);
      paramCount -= 1;
      for (; paramCount >= 0; paramCount--) {
        const [key, value] = paramAssoc[paramCount];
        paramIndexMap[key] = value;
      }
      return [h, paramIndexMap];
    });
  }
  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();
  for (let i = 0, len = handlerData.length; i < len; i++) {
    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {
      const map = handlerData[i][j]?.[1];
      if (!map) {
        continue;
      }
      const keys = Object.keys(map);
      for (let k = 0, len3 = keys.length; k < len3; k++) {
        map[keys[k]] = paramReplacementMap[map[keys[k]]];
      }
    }
  }
  const handlerMap = [];
  for (const i in indexReplacementMap) {
    handlerMap[i] = handlerData[indexReplacementMap[i]];
  }
  return [regexp, handlerMap, staticMap];
}
__name(buildMatcherFromPreprocessedRoutes, "buildMatcherFromPreprocessedRoutes");
function findMiddleware(middleware, path) {
  if (!middleware) {
    return void 0;
  }
  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {
    if (buildWildcardRegExp(k).test(path)) {
      return [...middleware[k]];
    }
  }
  return void 0;
}
__name(findMiddleware, "findMiddleware");
var RegExpRouter = class {
  static {
    __name(this, "RegExpRouter");
  }
  name = "RegExpRouter";
  #middleware;
  #routes;
  constructor() {
    this.#middleware = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };
    this.#routes = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };
  }
  add(method, path, handler) {
    const middleware = this.#middleware;
    const routes = this.#routes;
    if (!middleware || !routes) {
      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);
    }
    if (!middleware[method]) {
      ;
      [middleware, routes].forEach((handlerMap) => {
        handlerMap[method] = /* @__PURE__ */ Object.create(null);
        Object.keys(handlerMap[METHOD_NAME_ALL]).forEach((p) => {
          handlerMap[method][p] = [...handlerMap[METHOD_NAME_ALL][p]];
        });
      });
    }
    if (path === "/*") {
      path = "*";
    }
    const paramCount = (path.match(/\/:/g) || []).length;
    if (/\*$/.test(path)) {
      const re = buildWildcardRegExp(path);
      if (method === METHOD_NAME_ALL) {
        Object.keys(middleware).forEach((m) => {
          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];
        });
      } else {
        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];
      }
      Object.keys(middleware).forEach((m) => {
        if (method === METHOD_NAME_ALL || method === m) {
          Object.keys(middleware[m]).forEach((p) => {
            re.test(p) && middleware[m][p].push([handler, paramCount]);
          });
        }
      });
      Object.keys(routes).forEach((m) => {
        if (method === METHOD_NAME_ALL || method === m) {
          Object.keys(routes[m]).forEach(
            (p) => re.test(p) && routes[m][p].push([handler, paramCount])
          );
        }
      });
      return;
    }
    const paths = checkOptionalParameter(path) || [path];
    for (let i = 0, len = paths.length; i < len; i++) {
      const path2 = paths[i];
      Object.keys(routes).forEach((m) => {
        if (method === METHOD_NAME_ALL || method === m) {
          routes[m][path2] ||= [
            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[METHOD_NAME_ALL], path2) || []
          ];
          routes[m][path2].push([handler, paramCount - len + i + 1]);
        }
      });
    }
  }
  match(method, path) {
    clearWildcardRegExpCache();
    const matchers = this.#buildAllMatchers();
    this.match = (method2, path2) => {
      const matcher = matchers[method2] || matchers[METHOD_NAME_ALL];
      const staticMatch = matcher[2][path2];
      if (staticMatch) {
        return staticMatch;
      }
      const match = path2.match(matcher[0]);
      if (!match) {
        return [[], emptyParam];
      }
      const index = match.indexOf("", 1);
      return [matcher[1][index], match];
    };
    return this.match(method, path);
  }
  #buildAllMatchers() {
    const matchers = /* @__PURE__ */ Object.create(null);
    Object.keys(this.#routes).concat(Object.keys(this.#middleware)).forEach((method) => {
      matchers[method] ||= this.#buildMatcher(method);
    });
    this.#middleware = this.#routes = void 0;
    return matchers;
  }
  #buildMatcher(method) {
    const routes = [];
    let hasOwnRoute = method === METHOD_NAME_ALL;
    [this.#middleware, this.#routes].forEach((r) => {
      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];
      if (ownRoute.length !== 0) {
        hasOwnRoute ||= true;
        routes.push(...ownRoute);
      } else if (method !== METHOD_NAME_ALL) {
        routes.push(
          ...Object.keys(r[METHOD_NAME_ALL]).map((path) => [path, r[METHOD_NAME_ALL][path]])
        );
      }
    });
    if (!hasOwnRoute) {
      return null;
    } else {
      return buildMatcherFromPreprocessedRoutes(routes);
    }
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/smart-router/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/smart-router/router.js
init_modules_watch_stub();
var SmartRouter = class {
  static {
    __name(this, "SmartRouter");
  }
  name = "SmartRouter";
  #routers = [];
  #routes = [];
  constructor(init) {
    this.#routers = init.routers;
  }
  add(method, path, handler) {
    if (!this.#routes) {
      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);
    }
    this.#routes.push([method, path, handler]);
  }
  match(method, path) {
    if (!this.#routes) {
      throw new Error("Fatal error");
    }
    const routers = this.#routers;
    const routes = this.#routes;
    const len = routers.length;
    let i = 0;
    let res;
    for (; i < len; i++) {
      const router = routers[i];
      try {
        for (let i2 = 0, len2 = routes.length; i2 < len2; i2++) {
          router.add(...routes[i2]);
        }
        res = router.match(method, path);
      } catch (e) {
        if (e instanceof UnsupportedPathError) {
          continue;
        }
        throw e;
      }
      this.match = router.match.bind(router);
      this.#routers = [router];
      this.#routes = void 0;
      break;
    }
    if (i === len) {
      throw new Error("Fatal error");
    }
    this.name = `SmartRouter + ${this.activeRouter.name}`;
    return res;
  }
  get activeRouter() {
    if (this.#routes || this.#routers.length !== 1) {
      throw new Error("No active router has been determined yet.");
    }
    return this.#routers[0];
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/router.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/node.js
init_modules_watch_stub();
var emptyParams = /* @__PURE__ */ Object.create(null);
var Node2 = class {
  static {
    __name(this, "Node");
  }
  #methods;
  #children;
  #patterns;
  #order = 0;
  #params = emptyParams;
  constructor(method, handler, children) {
    this.#children = children || /* @__PURE__ */ Object.create(null);
    this.#methods = [];
    if (method && handler) {
      const m = /* @__PURE__ */ Object.create(null);
      m[method] = { handler, possibleKeys: [], score: 0 };
      this.#methods = [m];
    }
    this.#patterns = [];
  }
  insert(method, path, handler) {
    this.#order = ++this.#order;
    let curNode = this;
    const parts = splitRoutingPath(path);
    const possibleKeys = [];
    for (let i = 0, len = parts.length; i < len; i++) {
      const p = parts[i];
      const nextP = parts[i + 1];
      const pattern = getPattern(p, nextP);
      const key = Array.isArray(pattern) ? pattern[0] : p;
      if (key in curNode.#children) {
        curNode = curNode.#children[key];
        if (pattern) {
          possibleKeys.push(pattern[1]);
        }
        continue;
      }
      curNode.#children[key] = new Node2();
      if (pattern) {
        curNode.#patterns.push(pattern);
        possibleKeys.push(pattern[1]);
      }
      curNode = curNode.#children[key];
    }
    curNode.#methods.push({
      [method]: {
        handler,
        possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),
        score: this.#order
      }
    });
    return curNode;
  }
  #getHandlerSets(node, method, nodeParams, params) {
    const handlerSets = [];
    for (let i = 0, len = node.#methods.length; i < len; i++) {
      const m = node.#methods[i];
      const handlerSet = m[method] || m[METHOD_NAME_ALL];
      const processedSet = {};
      if (handlerSet !== void 0) {
        handlerSet.params = /* @__PURE__ */ Object.create(null);
        handlerSets.push(handlerSet);
        if (nodeParams !== emptyParams || params && params !== emptyParams) {
          for (let i2 = 0, len2 = handlerSet.possibleKeys.length; i2 < len2; i2++) {
            const key = handlerSet.possibleKeys[i2];
            const processed = processedSet[handlerSet.score];
            handlerSet.params[key] = params?.[key] && !processed ? params[key] : nodeParams[key] ?? params?.[key];
            processedSet[handlerSet.score] = true;
          }
        }
      }
    }
    return handlerSets;
  }
  search(method, path) {
    const handlerSets = [];
    this.#params = emptyParams;
    const curNode = this;
    let curNodes = [curNode];
    const parts = splitPath(path);
    const curNodesQueue = [];
    for (let i = 0, len = parts.length; i < len; i++) {
      const part = parts[i];
      const isLast = i === len - 1;
      const tempNodes = [];
      for (let j = 0, len2 = curNodes.length; j < len2; j++) {
        const node = curNodes[j];
        const nextNode = node.#children[part];
        if (nextNode) {
          nextNode.#params = node.#params;
          if (isLast) {
            if (nextNode.#children["*"]) {
              handlerSets.push(
                ...this.#getHandlerSets(nextNode.#children["*"], method, node.#params)
              );
            }
            handlerSets.push(...this.#getHandlerSets(nextNode, method, node.#params));
          } else {
            tempNodes.push(nextNode);
          }
        }
        for (let k = 0, len3 = node.#patterns.length; k < len3; k++) {
          const pattern = node.#patterns[k];
          const params = node.#params === emptyParams ? {} : { ...node.#params };
          if (pattern === "*") {
            const astNode = node.#children["*"];
            if (astNode) {
              handlerSets.push(...this.#getHandlerSets(astNode, method, node.#params));
              astNode.#params = params;
              tempNodes.push(astNode);
            }
            continue;
          }
          if (!part) {
            continue;
          }
          const [key, name, matcher] = pattern;
          const child = node.#children[key];
          const restPathString = parts.slice(i).join("/");
          if (matcher instanceof RegExp) {
            const m = matcher.exec(restPathString);
            if (m) {
              params[name] = m[0];
              handlerSets.push(...this.#getHandlerSets(child, method, node.#params, params));
              if (Object.keys(child.#children).length) {
                child.#params = params;
                const componentCount = m[0].match(/\//)?.length ?? 0;
                const targetCurNodes = curNodesQueue[componentCount] ||= [];
                targetCurNodes.push(child);
              }
              continue;
            }
          }
          if (matcher === true || matcher.test(part)) {
            params[name] = part;
            if (isLast) {
              handlerSets.push(...this.#getHandlerSets(child, method, params, node.#params));
              if (child.#children["*"]) {
                handlerSets.push(
                  ...this.#getHandlerSets(child.#children["*"], method, params, node.#params)
                );
              }
            } else {
              child.#params = params;
              tempNodes.push(child);
            }
          }
        }
      }
      curNodes = tempNodes.concat(curNodesQueue.shift() ?? []);
    }
    if (handlerSets.length > 1) {
      handlerSets.sort((a, b) => {
        return a.score - b.score;
      });
    }
    return [handlerSets.map(({ handler, params }) => [handler, params])];
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/router.js
var TrieRouter = class {
  static {
    __name(this, "TrieRouter");
  }
  name = "TrieRouter";
  #node;
  constructor() {
    this.#node = new Node2();
  }
  add(method, path, handler) {
    const results = checkOptionalParameter(path);
    if (results) {
      for (let i = 0, len = results.length; i < len; i++) {
        this.#node.insert(method, results[i], handler);
      }
      return;
    }
    this.#node.insert(method, path, handler);
  }
  match(method, path) {
    return this.#node.search(method, path);
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono.js
var Hono2 = class extends Hono {
  static {
    __name(this, "Hono");
  }
  constructor(options = {}) {
    super(options);
    this.router = options.router ?? new SmartRouter({
      routers: [new RegExpRouter(), new TrieRouter()]
    });
  }
};

// src/middleware/common.ts
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/middleware/cors/index.js
init_modules_watch_stub();
var cors = /* @__PURE__ */ __name((options) => {
  const defaults = {
    origin: "*",
    allowMethods: ["GET", "HEAD", "PUT", "POST", "DELETE", "PATCH"],
    allowHeaders: [],
    exposeHeaders: []
  };
  const opts = {
    ...defaults,
    ...options
  };
  const findAllowOrigin = ((optsOrigin) => {
    if (typeof optsOrigin === "string") {
      if (optsOrigin === "*") {
        return () => optsOrigin;
      } else {
        return (origin) => optsOrigin === origin ? origin : null;
      }
    } else if (typeof optsOrigin === "function") {
      return optsOrigin;
    } else {
      return (origin) => optsOrigin.includes(origin) ? origin : null;
    }
  })(opts.origin);
  const findAllowMethods = ((optsAllowMethods) => {
    if (typeof optsAllowMethods === "function") {
      return optsAllowMethods;
    } else if (Array.isArray(optsAllowMethods)) {
      return () => optsAllowMethods;
    } else {
      return () => [];
    }
  })(opts.allowMethods);
  return /* @__PURE__ */ __name(async function cors2(c, next) {
    function set(key, value) {
      c.res.headers.set(key, value);
    }
    __name(set, "set");
    const allowOrigin = findAllowOrigin(c.req.header("origin") || "", c);
    if (allowOrigin) {
      set("Access-Control-Allow-Origin", allowOrigin);
    }
    if (opts.origin !== "*") {
      const existingVary = c.req.header("Vary");
      if (existingVary) {
        set("Vary", existingVary);
      } else {
        set("Vary", "Origin");
      }
    }
    if (opts.credentials) {
      set("Access-Control-Allow-Credentials", "true");
    }
    if (opts.exposeHeaders?.length) {
      set("Access-Control-Expose-Headers", opts.exposeHeaders.join(","));
    }
    if (c.req.method === "OPTIONS") {
      if (opts.maxAge != null) {
        set("Access-Control-Max-Age", opts.maxAge.toString());
      }
      const allowMethods = findAllowMethods(c.req.header("origin") || "", c);
      if (allowMethods.length) {
        set("Access-Control-Allow-Methods", allowMethods.join(","));
      }
      let headers = opts.allowHeaders;
      if (!headers?.length) {
        const requestHeaders = c.req.header("Access-Control-Request-Headers");
        if (requestHeaders) {
          headers = requestHeaders.split(/\s*,\s*/);
        }
      }
      if (headers?.length) {
        set("Access-Control-Allow-Headers", headers.join(","));
        c.res.headers.append("Vary", "Access-Control-Request-Headers");
      }
      c.res.headers.delete("Content-Length");
      c.res.headers.delete("Content-Type");
      return new Response(null, {
        headers: c.res.headers,
        status: 204,
        statusText: "No Content"
      });
    }
    await next();
  }, "cors2");
}, "cors");

// src/utils/response.ts
init_modules_watch_stub();
function success(c, message, data) {
  const response = {
    success: true,
    message,
    data: data || null
  };
  return c.json(response);
}
__name(success, "success");
function error(c, message, status = 400) {
  const response = {
    success: false,
    message,
    data: null
  };
  return c.json(response, status);
}
__name(error, "error");

// src/middleware/common.ts
function corsMiddleware() {
  return cors({
    origin: "*",
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization"],
    credentials: true
  });
}
__name(corsMiddleware, "corsMiddleware");
function bindingsMiddleware() {
  return async (c, next) => {
    if (!c.env.DB) {
      return error(c, "\u6570\u636E\u5E93\u4E0D\u53EF\u7528", 500);
    }
    if (!c.env.CACHE) {
      return error(c, "\u7F13\u5B58\u4E0D\u53EF\u7528", 500);
    }
    await next();
  };
}
__name(bindingsMiddleware, "bindingsMiddleware");

// src/routes/auth.ts
init_modules_watch_stub();

// src/utils/auth.ts
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/middleware/jwt/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/middleware/jwt/jwt.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/helper/cookie/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/cookie.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/http-exception.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/jwt.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/encode.js
init_modules_watch_stub();
var decodeBase64Url = /* @__PURE__ */ __name((str) => {
  return decodeBase64(str.replace(/_|-/g, (m) => ({ _: "/", "-": "+" })[m] ?? m));
}, "decodeBase64Url");
var encodeBase64Url = /* @__PURE__ */ __name((buf) => encodeBase64(buf).replace(/\/|\+/g, (m) => ({ "/": "_", "+": "-" })[m] ?? m), "encodeBase64Url");
var encodeBase64 = /* @__PURE__ */ __name((buf) => {
  let binary = "";
  const bytes = new Uint8Array(buf);
  for (let i = 0, len = bytes.length; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}, "encodeBase64");
var decodeBase64 = /* @__PURE__ */ __name((str) => {
  const binary = atob(str);
  const bytes = new Uint8Array(new ArrayBuffer(binary.length));
  const half = binary.length / 2;
  for (let i = 0, j = binary.length - 1; i <= half; i++, j--) {
    bytes[i] = binary.charCodeAt(i);
    bytes[j] = binary.charCodeAt(j);
  }
  return bytes;
}, "decodeBase64");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/jwa.js
init_modules_watch_stub();
var AlgorithmTypes = /* @__PURE__ */ ((AlgorithmTypes2) => {
  AlgorithmTypes2["HS256"] = "HS256";
  AlgorithmTypes2["HS384"] = "HS384";
  AlgorithmTypes2["HS512"] = "HS512";
  AlgorithmTypes2["RS256"] = "RS256";
  AlgorithmTypes2["RS384"] = "RS384";
  AlgorithmTypes2["RS512"] = "RS512";
  AlgorithmTypes2["PS256"] = "PS256";
  AlgorithmTypes2["PS384"] = "PS384";
  AlgorithmTypes2["PS512"] = "PS512";
  AlgorithmTypes2["ES256"] = "ES256";
  AlgorithmTypes2["ES384"] = "ES384";
  AlgorithmTypes2["ES512"] = "ES512";
  AlgorithmTypes2["EdDSA"] = "EdDSA";
  return AlgorithmTypes2;
})(AlgorithmTypes || {});

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/jws.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/helper/adapter/index.js
init_modules_watch_stub();
var knownUserAgents = {
  deno: "Deno",
  bun: "Bun",
  workerd: "Cloudflare-Workers",
  node: "Node.js"
};
var getRuntimeKey = /* @__PURE__ */ __name(() => {
  const global = globalThis;
  const userAgentSupported = typeof navigator !== "undefined" && true;
  if (userAgentSupported) {
    for (const [runtimeKey, userAgent] of Object.entries(knownUserAgents)) {
      if (checkUserAgentEquals(userAgent)) {
        return runtimeKey;
      }
    }
  }
  if (typeof global?.EdgeRuntime === "string") {
    return "edge-light";
  }
  if (global?.fastly !== void 0) {
    return "fastly";
  }
  if (global?.process?.release?.name === "node") {
    return "node";
  }
  return "other";
}, "getRuntimeKey");
var checkUserAgentEquals = /* @__PURE__ */ __name((platform) => {
  const userAgent = "Cloudflare-Workers";
  return userAgent.startsWith(platform);
}, "checkUserAgentEquals");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/types.js
init_modules_watch_stub();
var JwtAlgorithmNotImplemented = class extends Error {
  static {
    __name(this, "JwtAlgorithmNotImplemented");
  }
  constructor(alg) {
    super(`${alg} is not an implemented algorithm`);
    this.name = "JwtAlgorithmNotImplemented";
  }
};
var JwtTokenInvalid = class extends Error {
  static {
    __name(this, "JwtTokenInvalid");
  }
  constructor(token) {
    super(`invalid JWT token: ${token}`);
    this.name = "JwtTokenInvalid";
  }
};
var JwtTokenNotBefore = class extends Error {
  static {
    __name(this, "JwtTokenNotBefore");
  }
  constructor(token) {
    super(`token (${token}) is being used before it's valid`);
    this.name = "JwtTokenNotBefore";
  }
};
var JwtTokenExpired = class extends Error {
  static {
    __name(this, "JwtTokenExpired");
  }
  constructor(token) {
    super(`token (${token}) expired`);
    this.name = "JwtTokenExpired";
  }
};
var JwtTokenIssuedAt = class extends Error {
  static {
    __name(this, "JwtTokenIssuedAt");
  }
  constructor(currentTimestamp, iat) {
    super(
      `Invalid "iat" claim, must be a valid number lower than "${currentTimestamp}" (iat: "${iat}")`
    );
    this.name = "JwtTokenIssuedAt";
  }
};
var JwtHeaderInvalid = class extends Error {
  static {
    __name(this, "JwtHeaderInvalid");
  }
  constructor(header) {
    super(`jwt header is invalid: ${JSON.stringify(header)}`);
    this.name = "JwtHeaderInvalid";
  }
};
var JwtHeaderRequiresKid = class extends Error {
  static {
    __name(this, "JwtHeaderRequiresKid");
  }
  constructor(header) {
    super(`required "kid" in jwt header: ${JSON.stringify(header)}`);
    this.name = "JwtHeaderRequiresKid";
  }
};
var JwtTokenSignatureMismatched = class extends Error {
  static {
    __name(this, "JwtTokenSignatureMismatched");
  }
  constructor(token) {
    super(`token(${token}) signature mismatched`);
    this.name = "JwtTokenSignatureMismatched";
  }
};
var CryptoKeyUsage = /* @__PURE__ */ ((CryptoKeyUsage2) => {
  CryptoKeyUsage2["Encrypt"] = "encrypt";
  CryptoKeyUsage2["Decrypt"] = "decrypt";
  CryptoKeyUsage2["Sign"] = "sign";
  CryptoKeyUsage2["Verify"] = "verify";
  CryptoKeyUsage2["DeriveKey"] = "deriveKey";
  CryptoKeyUsage2["DeriveBits"] = "deriveBits";
  CryptoKeyUsage2["WrapKey"] = "wrapKey";
  CryptoKeyUsage2["UnwrapKey"] = "unwrapKey";
  return CryptoKeyUsage2;
})(CryptoKeyUsage || {});

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/utf8.js
init_modules_watch_stub();
var utf8Encoder = new TextEncoder();
var utf8Decoder = new TextDecoder();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/jws.js
async function signing(privateKey, alg, data) {
  const algorithm = getKeyAlgorithm(alg);
  const cryptoKey = await importPrivateKey(privateKey, algorithm);
  return await crypto.subtle.sign(algorithm, cryptoKey, data);
}
__name(signing, "signing");
async function verifying(publicKey, alg, signature, data) {
  const algorithm = getKeyAlgorithm(alg);
  const cryptoKey = await importPublicKey(publicKey, algorithm);
  return await crypto.subtle.verify(algorithm, cryptoKey, signature, data);
}
__name(verifying, "verifying");
function pemToBinary(pem) {
  return decodeBase64(pem.replace(/-+(BEGIN|END).*/g, "").replace(/\s/g, ""));
}
__name(pemToBinary, "pemToBinary");
async function importPrivateKey(key, alg) {
  if (!crypto.subtle || !crypto.subtle.importKey) {
    throw new Error("`crypto.subtle.importKey` is undefined. JWT auth middleware requires it.");
  }
  if (isCryptoKey(key)) {
    if (key.type !== "private" && key.type !== "secret") {
      throw new Error(
        `unexpected key type: CryptoKey.type is ${key.type}, expected private or secret`
      );
    }
    return key;
  }
  const usages = [CryptoKeyUsage.Sign];
  if (typeof key === "object") {
    return await crypto.subtle.importKey("jwk", key, alg, false, usages);
  }
  if (key.includes("PRIVATE")) {
    return await crypto.subtle.importKey("pkcs8", pemToBinary(key), alg, false, usages);
  }
  return await crypto.subtle.importKey("raw", utf8Encoder.encode(key), alg, false, usages);
}
__name(importPrivateKey, "importPrivateKey");
async function importPublicKey(key, alg) {
  if (!crypto.subtle || !crypto.subtle.importKey) {
    throw new Error("`crypto.subtle.importKey` is undefined. JWT auth middleware requires it.");
  }
  if (isCryptoKey(key)) {
    if (key.type === "public" || key.type === "secret") {
      return key;
    }
    key = await exportPublicJwkFrom(key);
  }
  if (typeof key === "string" && key.includes("PRIVATE")) {
    const privateKey = await crypto.subtle.importKey("pkcs8", pemToBinary(key), alg, true, [
      CryptoKeyUsage.Sign
    ]);
    key = await exportPublicJwkFrom(privateKey);
  }
  const usages = [CryptoKeyUsage.Verify];
  if (typeof key === "object") {
    return await crypto.subtle.importKey("jwk", key, alg, false, usages);
  }
  if (key.includes("PUBLIC")) {
    return await crypto.subtle.importKey("spki", pemToBinary(key), alg, false, usages);
  }
  return await crypto.subtle.importKey("raw", utf8Encoder.encode(key), alg, false, usages);
}
__name(importPublicKey, "importPublicKey");
async function exportPublicJwkFrom(privateKey) {
  if (privateKey.type !== "private") {
    throw new Error(`unexpected key type: ${privateKey.type}`);
  }
  if (!privateKey.extractable) {
    throw new Error("unexpected private key is unextractable");
  }
  const jwk = await crypto.subtle.exportKey("jwk", privateKey);
  const { kty } = jwk;
  const { alg, e, n } = jwk;
  const { crv, x, y } = jwk;
  return { kty, alg, e, n, crv, x, y, key_ops: [CryptoKeyUsage.Verify] };
}
__name(exportPublicJwkFrom, "exportPublicJwkFrom");
function getKeyAlgorithm(name) {
  switch (name) {
    case "HS256":
      return {
        name: "HMAC",
        hash: {
          name: "SHA-256"
        }
      };
    case "HS384":
      return {
        name: "HMAC",
        hash: {
          name: "SHA-384"
        }
      };
    case "HS512":
      return {
        name: "HMAC",
        hash: {
          name: "SHA-512"
        }
      };
    case "RS256":
      return {
        name: "RSASSA-PKCS1-v1_5",
        hash: {
          name: "SHA-256"
        }
      };
    case "RS384":
      return {
        name: "RSASSA-PKCS1-v1_5",
        hash: {
          name: "SHA-384"
        }
      };
    case "RS512":
      return {
        name: "RSASSA-PKCS1-v1_5",
        hash: {
          name: "SHA-512"
        }
      };
    case "PS256":
      return {
        name: "RSA-PSS",
        hash: {
          name: "SHA-256"
        },
        saltLength: 32
      };
    case "PS384":
      return {
        name: "RSA-PSS",
        hash: {
          name: "SHA-384"
        },
        saltLength: 48
      };
    case "PS512":
      return {
        name: "RSA-PSS",
        hash: {
          name: "SHA-512"
        },
        saltLength: 64
      };
    case "ES256":
      return {
        name: "ECDSA",
        hash: {
          name: "SHA-256"
        },
        namedCurve: "P-256"
      };
    case "ES384":
      return {
        name: "ECDSA",
        hash: {
          name: "SHA-384"
        },
        namedCurve: "P-384"
      };
    case "ES512":
      return {
        name: "ECDSA",
        hash: {
          name: "SHA-512"
        },
        namedCurve: "P-521"
      };
    case "EdDSA":
      return {
        name: "Ed25519",
        namedCurve: "Ed25519"
      };
    default:
      throw new JwtAlgorithmNotImplemented(name);
  }
}
__name(getKeyAlgorithm, "getKeyAlgorithm");
function isCryptoKey(key) {
  const runtime = getRuntimeKey();
  if (runtime === "node" && !!crypto.webcrypto) {
    return key instanceof crypto.webcrypto.CryptoKey;
  }
  return key instanceof CryptoKey;
}
__name(isCryptoKey, "isCryptoKey");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/jwt.js
var encodeJwtPart = /* @__PURE__ */ __name((part) => encodeBase64Url(utf8Encoder.encode(JSON.stringify(part)).buffer).replace(/=/g, ""), "encodeJwtPart");
var encodeSignaturePart = /* @__PURE__ */ __name((buf) => encodeBase64Url(buf).replace(/=/g, ""), "encodeSignaturePart");
var decodeJwtPart = /* @__PURE__ */ __name((part) => JSON.parse(utf8Decoder.decode(decodeBase64Url(part))), "decodeJwtPart");
function isTokenHeader(obj) {
  if (typeof obj === "object" && obj !== null) {
    const objWithAlg = obj;
    return "alg" in objWithAlg && Object.values(AlgorithmTypes).includes(objWithAlg.alg) && (!("typ" in objWithAlg) || objWithAlg.typ === "JWT");
  }
  return false;
}
__name(isTokenHeader, "isTokenHeader");
var sign = /* @__PURE__ */ __name(async (payload, privateKey, alg = "HS256") => {
  const encodedPayload = encodeJwtPart(payload);
  let encodedHeader;
  if (typeof privateKey === "object" && "alg" in privateKey) {
    alg = privateKey.alg;
    encodedHeader = encodeJwtPart({ alg, typ: "JWT", kid: privateKey.kid });
  } else {
    encodedHeader = encodeJwtPart({ alg, typ: "JWT" });
  }
  const partialToken = `${encodedHeader}.${encodedPayload}`;
  const signaturePart = await signing(privateKey, alg, utf8Encoder.encode(partialToken));
  const signature = encodeSignaturePart(signaturePart);
  return `${partialToken}.${signature}`;
}, "sign");
var verify = /* @__PURE__ */ __name(async (token, publicKey, alg = "HS256") => {
  const tokenParts = token.split(".");
  if (tokenParts.length !== 3) {
    throw new JwtTokenInvalid(token);
  }
  const { header, payload } = decode(token);
  if (!isTokenHeader(header)) {
    throw new JwtHeaderInvalid(header);
  }
  const now = Date.now() / 1e3 | 0;
  if (payload.nbf && payload.nbf > now) {
    throw new JwtTokenNotBefore(token);
  }
  if (payload.exp && payload.exp <= now) {
    throw new JwtTokenExpired(token);
  }
  if (payload.iat && now < payload.iat) {
    throw new JwtTokenIssuedAt(now, payload.iat);
  }
  const headerPayload = token.substring(0, token.lastIndexOf("."));
  const verified = await verifying(
    publicKey,
    alg,
    decodeBase64Url(tokenParts[2]),
    utf8Encoder.encode(headerPayload)
  );
  if (!verified) {
    throw new JwtTokenSignatureMismatched(token);
  }
  return payload;
}, "verify");
var verifyFromJwks = /* @__PURE__ */ __name(async (token, options, init) => {
  const header = decodeHeader(token);
  if (!isTokenHeader(header)) {
    throw new JwtHeaderInvalid(header);
  }
  if (!header.kid) {
    throw new JwtHeaderRequiresKid(header);
  }
  if (options.jwks_uri) {
    const response = await fetch(options.jwks_uri, init);
    if (!response.ok) {
      throw new Error(`failed to fetch JWKS from ${options.jwks_uri}`);
    }
    const data = await response.json();
    if (!data.keys) {
      throw new Error('invalid JWKS response. "keys" field is missing');
    }
    if (!Array.isArray(data.keys)) {
      throw new Error('invalid JWKS response. "keys" field is not an array');
    }
    if (options.keys) {
      options.keys.push(...data.keys);
    } else {
      options.keys = data.keys;
    }
  } else if (!options.keys) {
    throw new Error('verifyFromJwks requires options for either "keys" or "jwks_uri" or both');
  }
  const matchingKey = options.keys.find((key) => key.kid === header.kid);
  if (!matchingKey) {
    throw new JwtTokenInvalid(token);
  }
  return await verify(token, matchingKey, matchingKey.alg || header.alg);
}, "verifyFromJwks");
var decode = /* @__PURE__ */ __name((token) => {
  try {
    const [h, p] = token.split(".");
    const header = decodeJwtPart(h);
    const payload = decodeJwtPart(p);
    return {
      header,
      payload
    };
  } catch {
    throw new JwtTokenInvalid(token);
  }
}, "decode");
var decodeHeader = /* @__PURE__ */ __name((token) => {
  try {
    const [h] = token.split(".");
    return decodeJwtPart(h);
  } catch {
    throw new JwtTokenInvalid(token);
  }
}, "decodeHeader");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/jwt/index.js
var Jwt = { sign, verify, decode, verifyFromJwks };

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/middleware/jwt/jwt.js
var verify2 = Jwt.verify;
var decode2 = Jwt.decode;
var sign2 = Jwt.sign;

// src/utils/auth.ts
var JWTService = class {
  static {
    __name(this, "JWTService");
  }
  secret;
  constructor(secret) {
    this.secret = secret;
  }
  async generateToken(payload) {
    const now = Math.floor(Date.now() / 1e3);
    const jwtPayload = {
      ...payload,
      iat: now,
      exp: now + 24 * 60 * 60
      // 24小时过期
    };
    return await sign2(jwtPayload, this.secret);
  }
  async generateRefreshToken(payload) {
    const now = Math.floor(Date.now() / 1e3);
    const jwtPayload = {
      ...payload,
      iat: now,
      exp: now + 7 * 24 * 60 * 60
      // 7天过期
    };
    return await sign2(jwtPayload, this.secret);
  }
  async verifyToken(token) {
    return await verify2(token, this.secret);
  }
};
function generateRandomKey(length = 32) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
__name(generateRandomKey, "generateRandomKey");

// node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js
init_modules_watch_stub();
var import_crypto = __toESM(require_crypto(), 1);
var randomFallback = null;
function randomBytes(len) {
  try {
    return crypto.getRandomValues(new Uint8Array(len));
  } catch {
  }
  try {
    return import_crypto.default.randomBytes(len);
  } catch {
  }
  if (!randomFallback) {
    throw Error(
      "Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative"
    );
  }
  return randomFallback(len);
}
__name(randomBytes, "randomBytes");
function genSaltSync(rounds, seed_length) {
  rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;
  if (typeof rounds !== "number")
    throw Error(
      "Illegal arguments: " + typeof rounds + ", " + typeof seed_length
    );
  if (rounds < 4) rounds = 4;
  else if (rounds > 31) rounds = 31;
  var salt = [];
  salt.push("$2b$");
  if (rounds < 10) salt.push("0");
  salt.push(rounds.toString());
  salt.push("$");
  salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN));
  return salt.join("");
}
__name(genSaltSync, "genSaltSync");
function genSalt(rounds, seed_length, callback) {
  if (typeof seed_length === "function")
    callback = seed_length, seed_length = void 0;
  if (typeof rounds === "function") callback = rounds, rounds = void 0;
  if (typeof rounds === "undefined") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;
  else if (typeof rounds !== "number")
    throw Error("illegal arguments: " + typeof rounds);
  function _async(callback2) {
    nextTick(function() {
      try {
        callback2(null, genSaltSync(rounds));
      } catch (err) {
        callback2(err);
      }
    });
  }
  __name(_async, "_async");
  if (callback) {
    if (typeof callback !== "function")
      throw Error("Illegal callback: " + typeof callback);
    _async(callback);
  } else
    return new Promise(function(resolve, reject) {
      _async(function(err, res) {
        if (err) {
          reject(err);
          return;
        }
        resolve(res);
      });
    });
}
__name(genSalt, "genSalt");
function hash(password, salt, callback, progressCallback) {
  function _async(callback2) {
    if (typeof password === "string" && typeof salt === "number")
      genSalt(salt, function(err, salt2) {
        _hash(password, salt2, callback2, progressCallback);
      });
    else if (typeof password === "string" && typeof salt === "string")
      _hash(password, salt, callback2, progressCallback);
    else
      nextTick(
        callback2.bind(
          this,
          Error("Illegal arguments: " + typeof password + ", " + typeof salt)
        )
      );
  }
  __name(_async, "_async");
  if (callback) {
    if (typeof callback !== "function")
      throw Error("Illegal callback: " + typeof callback);
    _async(callback);
  } else
    return new Promise(function(resolve, reject) {
      _async(function(err, res) {
        if (err) {
          reject(err);
          return;
        }
        resolve(res);
      });
    });
}
__name(hash, "hash");
function safeStringCompare(known, unknown) {
  var diff = known.length ^ unknown.length;
  for (var i = 0; i < known.length; ++i) {
    diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);
  }
  return diff === 0;
}
__name(safeStringCompare, "safeStringCompare");
function compare(password, hashValue, callback, progressCallback) {
  function _async(callback2) {
    if (typeof password !== "string" || typeof hashValue !== "string") {
      nextTick(
        callback2.bind(
          this,
          Error(
            "Illegal arguments: " + typeof password + ", " + typeof hashValue
          )
        )
      );
      return;
    }
    if (hashValue.length !== 60) {
      nextTick(callback2.bind(this, null, false));
      return;
    }
    hash(
      password,
      hashValue.substring(0, 29),
      function(err, comp) {
        if (err) callback2(err);
        else callback2(null, safeStringCompare(comp, hashValue));
      },
      progressCallback
    );
  }
  __name(_async, "_async");
  if (callback) {
    if (typeof callback !== "function")
      throw Error("Illegal callback: " + typeof callback);
    _async(callback);
  } else
    return new Promise(function(resolve, reject) {
      _async(function(err, res) {
        if (err) {
          reject(err);
          return;
        }
        resolve(res);
      });
    });
}
__name(compare, "compare");
var nextTick = typeof process !== "undefined" && process && typeof process.nextTick === "function" ? typeof setImmediate === "function" ? setImmediate : process.nextTick : setTimeout;
function utf8Length(string) {
  var len = 0, c = 0;
  for (var i = 0; i < string.length; ++i) {
    c = string.charCodeAt(i);
    if (c < 128) len += 1;
    else if (c < 2048) len += 2;
    else if ((c & 64512) === 55296 && (string.charCodeAt(i + 1) & 64512) === 56320) {
      ++i;
      len += 4;
    } else len += 3;
  }
  return len;
}
__name(utf8Length, "utf8Length");
function utf8Array(string) {
  var offset = 0, c1, c2;
  var buffer = new Array(utf8Length(string));
  for (var i = 0, k = string.length; i < k; ++i) {
    c1 = string.charCodeAt(i);
    if (c1 < 128) {
      buffer[offset++] = c1;
    } else if (c1 < 2048) {
      buffer[offset++] = c1 >> 6 | 192;
      buffer[offset++] = c1 & 63 | 128;
    } else if ((c1 & 64512) === 55296 && ((c2 = string.charCodeAt(i + 1)) & 64512) === 56320) {
      c1 = 65536 + ((c1 & 1023) << 10) + (c2 & 1023);
      ++i;
      buffer[offset++] = c1 >> 18 | 240;
      buffer[offset++] = c1 >> 12 & 63 | 128;
      buffer[offset++] = c1 >> 6 & 63 | 128;
      buffer[offset++] = c1 & 63 | 128;
    } else {
      buffer[offset++] = c1 >> 12 | 224;
      buffer[offset++] = c1 >> 6 & 63 | 128;
      buffer[offset++] = c1 & 63 | 128;
    }
  }
  return buffer;
}
__name(utf8Array, "utf8Array");
var BASE64_CODE = "./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split("");
var BASE64_INDEX = [
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  0,
  1,
  54,
  55,
  56,
  57,
  58,
  59,
  60,
  61,
  62,
  63,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  18,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  27,
  -1,
  -1,
  -1,
  -1,
  -1,
  -1,
  28,
  29,
  30,
  31,
  32,
  33,
  34,
  35,
  36,
  37,
  38,
  39,
  40,
  41,
  42,
  43,
  44,
  45,
  46,
  47,
  48,
  49,
  50,
  51,
  52,
  53,
  -1,
  -1,
  -1,
  -1,
  -1
];
function base64_encode(b, len) {
  var off = 0, rs = [], c1, c2;
  if (len <= 0 || len > b.length) throw Error("Illegal len: " + len);
  while (off < len) {
    c1 = b[off++] & 255;
    rs.push(BASE64_CODE[c1 >> 2 & 63]);
    c1 = (c1 & 3) << 4;
    if (off >= len) {
      rs.push(BASE64_CODE[c1 & 63]);
      break;
    }
    c2 = b[off++] & 255;
    c1 |= c2 >> 4 & 15;
    rs.push(BASE64_CODE[c1 & 63]);
    c1 = (c2 & 15) << 2;
    if (off >= len) {
      rs.push(BASE64_CODE[c1 & 63]);
      break;
    }
    c2 = b[off++] & 255;
    c1 |= c2 >> 6 & 3;
    rs.push(BASE64_CODE[c1 & 63]);
    rs.push(BASE64_CODE[c2 & 63]);
  }
  return rs.join("");
}
__name(base64_encode, "base64_encode");
function base64_decode(s, len) {
  var off = 0, slen = s.length, olen = 0, rs = [], c1, c2, c3, c4, o, code;
  if (len <= 0) throw Error("Illegal len: " + len);
  while (off < slen - 1 && olen < len) {
    code = s.charCodeAt(off++);
    c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
    code = s.charCodeAt(off++);
    c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
    if (c1 == -1 || c2 == -1) break;
    o = c1 << 2 >>> 0;
    o |= (c2 & 48) >> 4;
    rs.push(String.fromCharCode(o));
    if (++olen >= len || off >= slen) break;
    code = s.charCodeAt(off++);
    c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
    if (c3 == -1) break;
    o = (c2 & 15) << 4 >>> 0;
    o |= (c3 & 60) >> 2;
    rs.push(String.fromCharCode(o));
    if (++olen >= len || off >= slen) break;
    code = s.charCodeAt(off++);
    c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;
    o = (c3 & 3) << 6 >>> 0;
    o |= c4;
    rs.push(String.fromCharCode(o));
    ++olen;
  }
  var res = [];
  for (off = 0; off < olen; off++) res.push(rs[off].charCodeAt(0));
  return res;
}
__name(base64_decode, "base64_decode");
var BCRYPT_SALT_LEN = 16;
var GENSALT_DEFAULT_LOG2_ROUNDS = 10;
var BLOWFISH_NUM_ROUNDS = 16;
var MAX_EXECUTION_TIME = 100;
var P_ORIG = [
  608135816,
  2242054355,
  320440878,
  57701188,
  2752067618,
  698298832,
  137296536,
  3964562569,
  1160258022,
  953160567,
  3193202383,
  887688300,
  3232508343,
  3380367581,
  1065670069,
  3041331479,
  2450970073,
  2306472731
];
var S_ORIG = [
  3509652390,
  2564797868,
  805139163,
  3491422135,
  3101798381,
  1780907670,
  3128725573,
  4046225305,
  614570311,
  3012652279,
  134345442,
  2240740374,
  1667834072,
  1901547113,
  2757295779,
  4103290238,
  227898511,
  1921955416,
  1904987480,
  2182433518,
  2069144605,
  3260701109,
  2620446009,
  720527379,
  3318853667,
  677414384,
  3393288472,
  3101374703,
  2390351024,
  1614419982,
  1822297739,
  2954791486,
  3608508353,
  3174124327,
  2024746970,
  1432378464,
  3864339955,
  2857741204,
  1464375394,
  1676153920,
  1439316330,
  715854006,
  3033291828,
  289532110,
  2706671279,
  2087905683,
  3018724369,
  1668267050,
  732546397,
  1947742710,
  3462151702,
  2609353502,
  2950085171,
  1814351708,
  2050118529,
  680887927,
  999245976,
  1800124847,
  3300911131,
  1713906067,
  1641548236,
  4213287313,
  1216130144,
  1575780402,
  4018429277,
  3917837745,
  3693486850,
  3949271944,
  596196993,
  3549867205,
  258830323,
  2213823033,
  772490370,
  2760122372,
  1774776394,
  2652871518,
  566650946,
  4142492826,
  1728879713,
  2882767088,
  1783734482,
  3629395816,
  2517608232,
  2874225571,
  1861159788,
  326777828,
  3124490320,
  2130389656,
  2716951837,
  967770486,
  1724537150,
  2185432712,
  2364442137,
  1164943284,
  2105845187,
  998989502,
  3765401048,
  2244026483,
  1075463327,
  1455516326,
  1322494562,
  910128902,
  469688178,
  1117454909,
  936433444,
  3490320968,
  3675253459,
  1240580251,
  122909385,
  2157517691,
  634681816,
  4142456567,
  3825094682,
  3061402683,
  2540495037,
  79693498,
  3249098678,
  1084186820,
  1583128258,
  426386531,
  1761308591,
  1047286709,
  322548459,
  995290223,
  1845252383,
  2603652396,
  3431023940,
  2942221577,
  3202600964,
  3727903485,
  1712269319,
  422464435,
  3234572375,
  1170764815,
  3523960633,
  3117677531,
  1434042557,
  442511882,
  3600875718,
  1076654713,
  1738483198,
  4213154764,
  2393238008,
  3677496056,
  1014306527,
  4251020053,
  793779912,
  2902807211,
  842905082,
  4246964064,
  1395751752,
  1040244610,
  2656851899,
  3396308128,
  445077038,
  3742853595,
  3577915638,
  679411651,
  2892444358,
  2354009459,
  1767581616,
  3150600392,
  3791627101,
  3102740896,
  284835224,
  4246832056,
  1258075500,
  768725851,
  2589189241,
  3069724005,
  3532540348,
  1274779536,
  3789419226,
  2764799539,
  1660621633,
  3471099624,
  4011903706,
  913787905,
  3497959166,
  737222580,
  2514213453,
  2928710040,
  3937242737,
  1804850592,
  3499020752,
  2949064160,
  2386320175,
  2390070455,
  2415321851,
  4061277028,
  2290661394,
  2416832540,
  1336762016,
  1754252060,
  3520065937,
  3014181293,
  791618072,
  3188594551,
  3933548030,
  2332172193,
  3852520463,
  3043980520,
  413987798,
  3465142937,
  3030929376,
  4245938359,
  2093235073,
  3534596313,
  375366246,
  2157278981,
  2479649556,
  555357303,
  3870105701,
  2008414854,
  3344188149,
  4221384143,
  3956125452,
  2067696032,
  3594591187,
  2921233993,
  2428461,
  544322398,
  577241275,
  1471733935,
  610547355,
  4027169054,
  1432588573,
  1507829418,
  2025931657,
  3646575487,
  545086370,
  48609733,
  2200306550,
  1653985193,
  298326376,
  1316178497,
  3007786442,
  2064951626,
  458293330,
  2589141269,
  3591329599,
  3164325604,
  727753846,
  2179363840,
  146436021,
  1461446943,
  4069977195,
  705550613,
  3059967265,
  3887724982,
  4281599278,
  3313849956,
  1404054877,
  2845806497,
  146425753,
  1854211946,
  1266315497,
  3048417604,
  3681880366,
  3289982499,
  290971e4,
  1235738493,
  2632868024,
  2414719590,
  3970600049,
  1771706367,
  1449415276,
  3266420449,
  422970021,
  1963543593,
  2690192192,
  3826793022,
  1062508698,
  1531092325,
  1804592342,
  2583117782,
  2714934279,
  4024971509,
  1294809318,
  4028980673,
  1289560198,
  2221992742,
  1669523910,
  35572830,
  157838143,
  1052438473,
  1016535060,
  1802137761,
  1753167236,
  1386275462,
  3080475397,
  2857371447,
  1040679964,
  2145300060,
  2390574316,
  1461121720,
  2956646967,
  4031777805,
  4028374788,
  33600511,
  2920084762,
  1018524850,
  629373528,
  3691585981,
  3515945977,
  2091462646,
  2486323059,
  586499841,
  988145025,
  935516892,
  3367335476,
  2599673255,
  2839830854,
  265290510,
  3972581182,
  2759138881,
  3795373465,
  1005194799,
  847297441,
  406762289,
  1314163512,
  1332590856,
  1866599683,
  4127851711,
  750260880,
  613907577,
  1450815602,
  3165620655,
  3734664991,
  3650291728,
  3012275730,
  3704569646,
  1427272223,
  778793252,
  1343938022,
  2676280711,
  2052605720,
  1946737175,
  3164576444,
  3914038668,
  3967478842,
  3682934266,
  1661551462,
  3294938066,
  4011595847,
  840292616,
  3712170807,
  616741398,
  312560963,
  711312465,
  1351876610,
  322626781,
  1910503582,
  271666773,
  2175563734,
  1594956187,
  70604529,
  3617834859,
  1007753275,
  1495573769,
  4069517037,
  2549218298,
  2663038764,
  504708206,
  2263041392,
  3941167025,
  2249088522,
  1514023603,
  1998579484,
  1312622330,
  694541497,
  2582060303,
  2151582166,
  1382467621,
  776784248,
  2618340202,
  3323268794,
  2497899128,
  2784771155,
  503983604,
  4076293799,
  907881277,
  423175695,
  432175456,
  1378068232,
  4145222326,
  3954048622,
  3938656102,
  3820766613,
  2793130115,
  2977904593,
  26017576,
  3274890735,
  3194772133,
  1700274565,
  1756076034,
  4006520079,
  3677328699,
  720338349,
  1533947780,
  354530856,
  688349552,
  3973924725,
  1637815568,
  332179504,
  3949051286,
  53804574,
  2852348879,
  3044236432,
  1282449977,
  3583942155,
  3416972820,
  4006381244,
  1617046695,
  2628476075,
  3002303598,
  1686838959,
  431878346,
  2686675385,
  1700445008,
  1080580658,
  1009431731,
  832498133,
  3223435511,
  2605976345,
  2271191193,
  2516031870,
  1648197032,
  4164389018,
  2548247927,
  300782431,
  375919233,
  238389289,
  3353747414,
  2531188641,
  2019080857,
  1475708069,
  455242339,
  2609103871,
  448939670,
  3451063019,
  1395535956,
  2413381860,
  1841049896,
  1491858159,
  885456874,
  4264095073,
  4001119347,
  1565136089,
  3898914787,
  1108368660,
  540939232,
  1173283510,
  2745871338,
  3681308437,
  4207628240,
  3343053890,
  4016749493,
  1699691293,
  1103962373,
  3625875870,
  2256883143,
  3830138730,
  1031889488,
  3479347698,
  1535977030,
  4236805024,
  3251091107,
  2132092099,
  1774941330,
  1199868427,
  1452454533,
  157007616,
  2904115357,
  342012276,
  595725824,
  1480756522,
  206960106,
  497939518,
  591360097,
  863170706,
  2375253569,
  3596610801,
  1814182875,
  2094937945,
  3421402208,
  1082520231,
  3463918190,
  2785509508,
  435703966,
  3908032597,
  1641649973,
  2842273706,
  3305899714,
  1510255612,
  2148256476,
  2655287854,
  3276092548,
  4258621189,
  236887753,
  3681803219,
  274041037,
  1734335097,
  3815195456,
  3317970021,
  1899903192,
  1026095262,
  4050517792,
  356393447,
  2410691914,
  3873677099,
  3682840055,
  3913112168,
  2491498743,
  4132185628,
  2489919796,
  1091903735,
  1979897079,
  3170134830,
  3567386728,
  3557303409,
  857797738,
  1136121015,
  1342202287,
  507115054,
  2535736646,
  337727348,
  3213592640,
  1301675037,
  2528481711,
  1895095763,
  1721773893,
  3216771564,
  62756741,
  2142006736,
  835421444,
  2531993523,
  1442658625,
  3659876326,
  2882144922,
  676362277,
  1392781812,
  170690266,
  3921047035,
  1759253602,
  3611846912,
  1745797284,
  664899054,
  1329594018,
  3901205900,
  3045908486,
  2062866102,
  2865634940,
  3543621612,
  3464012697,
  1080764994,
  553557557,
  3656615353,
  3996768171,
  991055499,
  499776247,
  1265440854,
  648242737,
  3940784050,
  980351604,
  3713745714,
  1749149687,
  3396870395,
  4211799374,
  3640570775,
  1161844396,
  3125318951,
  1431517754,
  545492359,
  4268468663,
  3499529547,
  1437099964,
  2702547544,
  3433638243,
  2581715763,
  2787789398,
  1060185593,
  1593081372,
  2418618748,
  4260947970,
  69676912,
  2159744348,
  86519011,
  2512459080,
  3838209314,
  1220612927,
  3339683548,
  133810670,
  1090789135,
  1078426020,
  1569222167,
  845107691,
  3583754449,
  4072456591,
  1091646820,
  628848692,
  1613405280,
  3757631651,
  526609435,
  236106946,
  48312990,
  2942717905,
  3402727701,
  1797494240,
  859738849,
  992217954,
  4005476642,
  2243076622,
  3870952857,
  3732016268,
  765654824,
  3490871365,
  2511836413,
  1685915746,
  3888969200,
  1414112111,
  2273134842,
  3281911079,
  4080962846,
  172450625,
  2569994100,
  980381355,
  4109958455,
  2819808352,
  2716589560,
  2568741196,
  3681446669,
  3329971472,
  1835478071,
  660984891,
  3704678404,
  4045999559,
  3422617507,
  3040415634,
  1762651403,
  1719377915,
  3470491036,
  2693910283,
  3642056355,
  3138596744,
  1364962596,
  2073328063,
  1983633131,
  926494387,
  3423689081,
  2150032023,
  4096667949,
  1749200295,
  3328846651,
  309677260,
  2016342300,
  1779581495,
  3079819751,
  111262694,
  1274766160,
  443224088,
  298511866,
  1025883608,
  3806446537,
  1145181785,
  168956806,
  3641502830,
  3584813610,
  1689216846,
  3666258015,
  3200248200,
  1692713982,
  2646376535,
  4042768518,
  1618508792,
  1610833997,
  3523052358,
  4130873264,
  2001055236,
  3610705100,
  2202168115,
  4028541809,
  2961195399,
  1006657119,
  2006996926,
  3186142756,
  1430667929,
  3210227297,
  1314452623,
  4074634658,
  4101304120,
  2273951170,
  1399257539,
  3367210612,
  3027628629,
  1190975929,
  2062231137,
  2333990788,
  2221543033,
  2438960610,
  1181637006,
  548689776,
  2362791313,
  3372408396,
  3104550113,
  3145860560,
  296247880,
  1970579870,
  3078560182,
  3769228297,
  1714227617,
  3291629107,
  3898220290,
  166772364,
  1251581989,
  493813264,
  448347421,
  195405023,
  2709975567,
  677966185,
  3703036547,
  1463355134,
  2715995803,
  1338867538,
  1343315457,
  2802222074,
  2684532164,
  233230375,
  2599980071,
  2000651841,
  3277868038,
  1638401717,
  4028070440,
  3237316320,
  6314154,
  819756386,
  300326615,
  590932579,
  1405279636,
  3267499572,
  3150704214,
  2428286686,
  3959192993,
  3461946742,
  1862657033,
  1266418056,
  963775037,
  2089974820,
  2263052895,
  1917689273,
  448879540,
  3550394620,
  3981727096,
  150775221,
  3627908307,
  1303187396,
  508620638,
  2975983352,
  2726630617,
  1817252668,
  1876281319,
  1457606340,
  908771278,
  3720792119,
  3617206836,
  2455994898,
  1729034894,
  1080033504,
  976866871,
  3556439503,
  2881648439,
  1522871579,
  1555064734,
  1336096578,
  3548522304,
  2579274686,
  3574697629,
  3205460757,
  3593280638,
  3338716283,
  3079412587,
  564236357,
  2993598910,
  1781952180,
  1464380207,
  3163844217,
  3332601554,
  1699332808,
  1393555694,
  1183702653,
  3581086237,
  1288719814,
  691649499,
  2847557200,
  2895455976,
  3193889540,
  2717570544,
  1781354906,
  1676643554,
  2592534050,
  3230253752,
  1126444790,
  2770207658,
  2633158820,
  2210423226,
  2615765581,
  2414155088,
  3127139286,
  673620729,
  2805611233,
  1269405062,
  4015350505,
  3341807571,
  4149409754,
  1057255273,
  2012875353,
  2162469141,
  2276492801,
  2601117357,
  993977747,
  3918593370,
  2654263191,
  753973209,
  36408145,
  2530585658,
  25011837,
  3520020182,
  2088578344,
  530523599,
  2918365339,
  1524020338,
  1518925132,
  3760827505,
  3759777254,
  1202760957,
  3985898139,
  3906192525,
  674977740,
  4174734889,
  2031300136,
  2019492241,
  3983892565,
  4153806404,
  3822280332,
  352677332,
  2297720250,
  60907813,
  90501309,
  3286998549,
  1016092578,
  2535922412,
  2839152426,
  457141659,
  509813237,
  4120667899,
  652014361,
  1966332200,
  2975202805,
  55981186,
  2327461051,
  676427537,
  3255491064,
  2882294119,
  3433927263,
  1307055953,
  942726286,
  933058658,
  2468411793,
  3933900994,
  4215176142,
  1361170020,
  2001714738,
  2830558078,
  3274259782,
  1222529897,
  1679025792,
  2729314320,
  3714953764,
  1770335741,
  151462246,
  3013232138,
  1682292957,
  1483529935,
  471910574,
  1539241949,
  458788160,
  3436315007,
  1807016891,
  3718408830,
  978976581,
  1043663428,
  3165965781,
  1927990952,
  4200891579,
  2372276910,
  3208408903,
  3533431907,
  1412390302,
  2931980059,
  4132332400,
  1947078029,
  3881505623,
  4168226417,
  2941484381,
  1077988104,
  1320477388,
  886195818,
  18198404,
  3786409e3,
  2509781533,
  112762804,
  3463356488,
  1866414978,
  891333506,
  18488651,
  661792760,
  1628790961,
  3885187036,
  3141171499,
  876946877,
  2693282273,
  1372485963,
  791857591,
  2686433993,
  3759982718,
  3167212022,
  3472953795,
  2716379847,
  445679433,
  3561995674,
  3504004811,
  3574258232,
  54117162,
  3331405415,
  2381918588,
  3769707343,
  4154350007,
  1140177722,
  4074052095,
  668550556,
  3214352940,
  367459370,
  261225585,
  2610173221,
  4209349473,
  3468074219,
  3265815641,
  314222801,
  3066103646,
  3808782860,
  282218597,
  3406013506,
  3773591054,
  379116347,
  1285071038,
  846784868,
  2669647154,
  3771962079,
  3550491691,
  2305946142,
  453669953,
  1268987020,
  3317592352,
  3279303384,
  3744833421,
  2610507566,
  3859509063,
  266596637,
  3847019092,
  517658769,
  3462560207,
  3443424879,
  370717030,
  4247526661,
  2224018117,
  4143653529,
  4112773975,
  2788324899,
  2477274417,
  1456262402,
  2901442914,
  1517677493,
  1846949527,
  2295493580,
  3734397586,
  2176403920,
  1280348187,
  1908823572,
  3871786941,
  846861322,
  1172426758,
  3287448474,
  3383383037,
  1655181056,
  3139813346,
  901632758,
  1897031941,
  2986607138,
  3066810236,
  3447102507,
  1393639104,
  373351379,
  950779232,
  625454576,
  3124240540,
  4148612726,
  2007998917,
  544563296,
  2244738638,
  2330496472,
  2058025392,
  1291430526,
  424198748,
  50039436,
  29584100,
  3605783033,
  2429876329,
  2791104160,
  1057563949,
  3255363231,
  3075367218,
  3463963227,
  1469046755,
  985887462
];
var C_ORIG = [
  1332899944,
  1700884034,
  1701343084,
  1684370003,
  1668446532,
  1869963892
];
function _encipher(lr, off, P, S) {
  var n, l = lr[off], r = lr[off + 1];
  l ^= P[0];
  n = S[l >>> 24];
  n += S[256 | l >> 16 & 255];
  n ^= S[512 | l >> 8 & 255];
  n += S[768 | l & 255];
  r ^= n ^ P[1];
  n = S[r >>> 24];
  n += S[256 | r >> 16 & 255];
  n ^= S[512 | r >> 8 & 255];
  n += S[768 | r & 255];
  l ^= n ^ P[2];
  n = S[l >>> 24];
  n += S[256 | l >> 16 & 255];
  n ^= S[512 | l >> 8 & 255];
  n += S[768 | l & 255];
  r ^= n ^ P[3];
  n = S[r >>> 24];
  n += S[256 | r >> 16 & 255];
  n ^= S[512 | r >> 8 & 255];
  n += S[768 | r & 255];
  l ^= n ^ P[4];
  n = S[l >>> 24];
  n += S[256 | l >> 16 & 255];
  n ^= S[512 | l >> 8 & 255];
  n += S[768 | l & 255];
  r ^= n ^ P[5];
  n = S[r >>> 24];
  n += S[256 | r >> 16 & 255];
  n ^= S[512 | r >> 8 & 255];
  n += S[768 | r & 255];
  l ^= n ^ P[6];
  n = S[l >>> 24];
  n += S[256 | l >> 16 & 255];
  n ^= S[512 | l >> 8 & 255];
  n += S[768 | l & 255];
  r ^= n ^ P[7];
  n = S[r >>> 24];
  n += S[256 | r >> 16 & 255];
  n ^= S[512 | r >> 8 & 255];
  n += S[768 | r & 255];
  l ^= n ^ P[8];
  n = S[l >>> 24];
  n += S[256 | l >> 16 & 255];
  n ^= S[512 | l >> 8 & 255];
  n += S[768 | l & 255];
  r ^= n ^ P[9];
  n = S[r >>> 24];
  n += S[256 | r >> 16 & 255];
  n ^= S[512 | r >> 8 & 255];
  n += S[768 | r & 255];
  l ^= n ^ P[10];
  n = S[l >>> 24];
  n += S[256 | l >> 16 & 255];
  n ^= S[512 | l >> 8 & 255];
  n += S[768 | l & 255];
  r ^= n ^ P[11];
  n = S[r >>> 24];
  n += S[256 | r >> 16 & 255];
  n ^= S[512 | r >> 8 & 255];
  n += S[768 | r & 255];
  l ^= n ^ P[12];
  n = S[l >>> 24];
  n += S[256 | l >> 16 & 255];
  n ^= S[512 | l >> 8 & 255];
  n += S[768 | l & 255];
  r ^= n ^ P[13];
  n = S[r >>> 24];
  n += S[256 | r >> 16 & 255];
  n ^= S[512 | r >> 8 & 255];
  n += S[768 | r & 255];
  l ^= n ^ P[14];
  n = S[l >>> 24];
  n += S[256 | l >> 16 & 255];
  n ^= S[512 | l >> 8 & 255];
  n += S[768 | l & 255];
  r ^= n ^ P[15];
  n = S[r >>> 24];
  n += S[256 | r >> 16 & 255];
  n ^= S[512 | r >> 8 & 255];
  n += S[768 | r & 255];
  l ^= n ^ P[16];
  lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];
  lr[off + 1] = l;
  return lr;
}
__name(_encipher, "_encipher");
function _streamtoword(data, offp) {
  for (var i = 0, word = 0; i < 4; ++i)
    word = word << 8 | data[offp] & 255, offp = (offp + 1) % data.length;
  return { key: word, offp };
}
__name(_streamtoword, "_streamtoword");
function _key(key, P, S) {
  var offset = 0, lr = [0, 0], plen = P.length, slen = S.length, sw;
  for (var i = 0; i < plen; i++)
    sw = _streamtoword(key, offset), offset = sw.offp, P[i] = P[i] ^ sw.key;
  for (i = 0; i < plen; i += 2)
    lr = _encipher(lr, 0, P, S), P[i] = lr[0], P[i + 1] = lr[1];
  for (i = 0; i < slen; i += 2)
    lr = _encipher(lr, 0, P, S), S[i] = lr[0], S[i + 1] = lr[1];
}
__name(_key, "_key");
function _ekskey(data, key, P, S) {
  var offp = 0, lr = [0, 0], plen = P.length, slen = S.length, sw;
  for (var i = 0; i < plen; i++)
    sw = _streamtoword(key, offp), offp = sw.offp, P[i] = P[i] ^ sw.key;
  offp = 0;
  for (i = 0; i < plen; i += 2)
    sw = _streamtoword(data, offp), offp = sw.offp, lr[0] ^= sw.key, sw = _streamtoword(data, offp), offp = sw.offp, lr[1] ^= sw.key, lr = _encipher(lr, 0, P, S), P[i] = lr[0], P[i + 1] = lr[1];
  for (i = 0; i < slen; i += 2)
    sw = _streamtoword(data, offp), offp = sw.offp, lr[0] ^= sw.key, sw = _streamtoword(data, offp), offp = sw.offp, lr[1] ^= sw.key, lr = _encipher(lr, 0, P, S), S[i] = lr[0], S[i + 1] = lr[1];
}
__name(_ekskey, "_ekskey");
function _crypt(b, salt, rounds, callback, progressCallback) {
  var cdata = C_ORIG.slice(), clen = cdata.length, err;
  if (rounds < 4 || rounds > 31) {
    err = Error("Illegal number of rounds (4-31): " + rounds);
    if (callback) {
      nextTick(callback.bind(this, err));
      return;
    } else throw err;
  }
  if (salt.length !== BCRYPT_SALT_LEN) {
    err = Error(
      "Illegal salt length: " + salt.length + " != " + BCRYPT_SALT_LEN
    );
    if (callback) {
      nextTick(callback.bind(this, err));
      return;
    } else throw err;
  }
  rounds = 1 << rounds >>> 0;
  var P, S, i = 0, j;
  if (typeof Int32Array === "function") {
    P = new Int32Array(P_ORIG);
    S = new Int32Array(S_ORIG);
  } else {
    P = P_ORIG.slice();
    S = S_ORIG.slice();
  }
  _ekskey(salt, b, P, S);
  function next() {
    if (progressCallback) progressCallback(i / rounds);
    if (i < rounds) {
      var start = Date.now();
      for (; i < rounds; ) {
        i = i + 1;
        _key(b, P, S);
        _key(salt, P, S);
        if (Date.now() - start > MAX_EXECUTION_TIME) break;
      }
    } else {
      for (i = 0; i < 64; i++)
        for (j = 0; j < clen >> 1; j++) _encipher(cdata, j << 1, P, S);
      var ret = [];
      for (i = 0; i < clen; i++)
        ret.push((cdata[i] >> 24 & 255) >>> 0), ret.push((cdata[i] >> 16 & 255) >>> 0), ret.push((cdata[i] >> 8 & 255) >>> 0), ret.push((cdata[i] & 255) >>> 0);
      if (callback) {
        callback(null, ret);
        return;
      } else return ret;
    }
    if (callback) nextTick(next);
  }
  __name(next, "next");
  if (typeof callback !== "undefined") {
    next();
  } else {
    var res;
    while (true) if (typeof (res = next()) !== "undefined") return res || [];
  }
}
__name(_crypt, "_crypt");
function _hash(password, salt, callback, progressCallback) {
  var err;
  if (typeof password !== "string" || typeof salt !== "string") {
    err = Error("Invalid string / salt: Not a string");
    if (callback) {
      nextTick(callback.bind(this, err));
      return;
    } else throw err;
  }
  var minor, offset;
  if (salt.charAt(0) !== "$" || salt.charAt(1) !== "2") {
    err = Error("Invalid salt version: " + salt.substring(0, 2));
    if (callback) {
      nextTick(callback.bind(this, err));
      return;
    } else throw err;
  }
  if (salt.charAt(2) === "$") minor = String.fromCharCode(0), offset = 3;
  else {
    minor = salt.charAt(2);
    if (minor !== "a" && minor !== "b" && minor !== "y" || salt.charAt(3) !== "$") {
      err = Error("Invalid salt revision: " + salt.substring(2, 4));
      if (callback) {
        nextTick(callback.bind(this, err));
        return;
      } else throw err;
    }
    offset = 4;
  }
  if (salt.charAt(offset + 2) > "$") {
    err = Error("Missing salt rounds");
    if (callback) {
      nextTick(callback.bind(this, err));
      return;
    } else throw err;
  }
  var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10, r2 = parseInt(salt.substring(offset + 1, offset + 2), 10), rounds = r1 + r2, real_salt = salt.substring(offset + 3, offset + 25);
  password += minor >= "a" ? "\0" : "";
  var passwordb = utf8Array(password), saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);
  function finish(bytes) {
    var res = [];
    res.push("$2");
    if (minor >= "a") res.push(minor);
    res.push("$");
    if (rounds < 10) res.push("0");
    res.push(rounds.toString());
    res.push("$");
    res.push(base64_encode(saltb, saltb.length));
    res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));
    return res.join("");
  }
  __name(finish, "finish");
  if (typeof callback == "undefined")
    return finish(_crypt(passwordb, saltb, rounds));
  else {
    _crypt(
      passwordb,
      saltb,
      rounds,
      function(err2, bytes) {
        if (err2) callback(err2, null);
        else callback(null, finish(bytes));
      },
      progressCallback
    );
  }
}
__name(_hash, "_hash");

// src/routes/auth.ts
function setupAuthRoutes(app2) {
  app2.post("/api/auth/login", async (c) => {
    try {
      const body = await c.req.json();
      const { username, password } = body;
      if (!username || !password) {
        return error(c, "\u7528\u6237\u540D\u548C\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A", 400);
      }
      const user = await c.env.DB.prepare(`
        SELECT id, username, password_hash, role, status, display_name, contact_wechat, avatar_url
        FROM users
        WHERE username = ? AND status = 'active'
      `).bind(username).first();
      if (!user) {
        return error(c, "\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF", 401);
      }
      const isPasswordValid = await compare(password, user.password_hash);
      if (!isPasswordValid) {
        return error(c, "\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF", 401);
      }
      const jwtService = new JWTService(c.env.JWT_SECRET);
      const tokenPayload = {
        admin_id: user.id,
        username: user.username,
        role: user.role
      };
      const token = await jwtService.generateToken(tokenPayload);
      let authorizedProducts = [];
      if (user.role === "distributor") {
        const authorizations = await c.env.DB.prepare(`
          SELECT version_id FROM distributor_authorizations 
          WHERE distributor_id = ? AND status = 'active'
        `).bind(user.id).all();
        authorizedProducts = authorizations.results.map((auth) => auth.version_id.toString());
      }
      const cacheKey = `user:auth:${user.id}`;
      await c.env.CACHE.put(cacheKey, JSON.stringify({
        id: user.id,
        username: user.username,
        role: user.role,
        display_name: user.display_name,
        contact_wechat: user.contact_wechat,
        avatar_url: user.avatar_url,
        authorized_products: authorizedProducts
      }), { expirationTtl: 3600 });
      return success(c, "\u767B\u5F55\u6210\u529F", {
        token,
        admin_id: user.id,
        username: user.username,
        role: user.role,
        authorized_products: authorizedProducts,
        expires_in: 86400
      });
    } catch (error2) {
      console.error("User login error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.post("/api/auth/refresh", async (c) => {
    try {
      const authHeader = c.req.header("Authorization");
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return error(c, "\u7F3A\u5C11\u6709\u6548\u7684\u5237\u65B0token", 401);
      }
      const refresh_token = authHeader.substring(7);
      const jwtService = new JWTService(c.env.JWT_SECRET);
      let payload;
      try {
        payload = await jwtService.verifyToken(refresh_token);
      } catch {
        return error(c, "\u65E0\u6548\u6216\u8FC7\u671F\u7684\u5237\u65B0token", 401);
      }
      const user = await c.env.DB.prepare(`
        SELECT id, username, role, status, display_name, contact_wechat, avatar_url
        FROM users
        WHERE id = ? AND status = 'active'
      `).bind(payload.admin_id).first();
      if (!user) {
        return error(c, "\u7528\u6237\u8D26\u53F7\u4E0D\u5B58\u5728\u6216\u5DF2\u7981\u7528", 401);
      }
      const tokenPayload = {
        admin_id: user.id,
        username: user.username,
        role: user.role
      };
      const token = await jwtService.generateToken(tokenPayload);
      let authorizedProducts = [];
      if (user.role === "distributor") {
        const authorizations = await c.env.DB.prepare(`
          SELECT version_id FROM distributor_authorizations 
          WHERE distributor_id = ? AND status = 'active'
        `).bind(user.id).all();
        authorizedProducts = authorizations.results.map((auth) => auth.version_id.toString());
      }
      const cacheKey = `user:auth:${user.id}`;
      await c.env.CACHE.put(cacheKey, JSON.stringify({
        id: user.id,
        username: user.username,
        role: user.role,
        display_name: user.display_name,
        contact_wechat: user.contact_wechat,
        avatar_url: user.avatar_url,
        authorized_products: authorizedProducts
      }), { expirationTtl: 3600 });
      return success(c, "Token\u5237\u65B0\u6210\u529F", {
        token,
        expires_in: 86400
      });
    } catch (error2) {
      console.error("Token refresh error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.post("/api/auth/logout", async (c) => {
    try {
      const authHeader = c.req.header("Authorization");
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return error(c, "\u9700\u8981\u6388\u6743\u5934", 401);
      }
      const token = authHeader.substring(7);
      const jwtService = new JWTService(c.env.JWT_SECRET);
      try {
        const payload = await jwtService.verifyToken(token);
        const cacheKey = `user:auth:${payload.admin_id}`;
        await c.env.CACHE.delete(cacheKey);
      } catch {
      }
      return success(c, "\u767B\u51FA\u6210\u529F");
    } catch (error2) {
      console.error("Logout error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
}
__name(setupAuthRoutes, "setupAuthRoutes");

// src/routes/user.ts
init_modules_watch_stub();

// src/middleware/auth.ts
init_modules_watch_stub();
function authMiddleware() {
  return async (c, next) => {
    const authHeader = c.req.header("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return error(c, "\u672A\u6388\u6743\u8BBF\u95EE\uFF1A\u7F3A\u5C11\u6216\u65E0\u6548\u7684token", 401);
    }
    const token = authHeader.substring(7);
    const jwtService = new JWTService(c.env.JWT_SECRET);
    try {
      const payload = await jwtService.verifyToken(token);
      if (payload.exp < Math.floor(Date.now() / 1e3)) {
        return error(c, "\u672A\u6388\u6743\u8BBF\u95EE\uFF1Atoken\u5DF2\u8FC7\u671F", 401);
      }
      c.set("admin", payload);
      await next();
    } catch (err) {
      return error(c, "\u672A\u6388\u6743\u8BBF\u95EE\uFF1A\u65E0\u6548\u7684token", 401);
    }
  };
}
__name(authMiddleware, "authMiddleware");
function adminOnly() {
  return async (c, next) => {
    const user = c.get("admin");
    if (!user || user.role !== "admin") {
      return error(c, "\u6743\u9650\u4E0D\u8DB3\uFF1A\u9700\u8981\u7BA1\u7406\u5458\u6743\u9650", 403);
    }
    await next();
  };
}
__name(adminOnly, "adminOnly");

// src/routes/user.ts
function setupUserRoutes(app2) {
  app2.use("/api/users", authMiddleware(), adminOnly());
  app2.use("/api/users/*", authMiddleware(), adminOnly());
  app2.get("/api/users", async (c) => {
    try {
      const query = c.req.query();
      const page = parseInt(query.page || "1");
      const limit = parseInt(query.limit || "20");
      const role = query.role;
      const status = query.status;
      const search = query.search;
      const offset = (page - 1) * limit;
      let queryStr = "SELECT id, username, role, status, display_name, contact_wechat, avatar_url, created_at, updated_at FROM users";
      const params = [];
      const conditions = [];
      if (role) {
        conditions.push("role = ?");
        params.push(role);
      }
      if (status) {
        conditions.push("status = ?");
        params.push(status);
      }
      if (search) {
        conditions.push("(username LIKE ? OR display_name LIKE ?)");
        params.push(`%${search}%`, `%${search}%`);
      }
      if (conditions.length > 0) {
        queryStr += " WHERE " + conditions.join(" AND ");
      }
      queryStr += " ORDER BY created_at DESC LIMIT ? OFFSET ?";
      params.push(limit, offset);
      const users = await c.env.DB.prepare(queryStr).bind(...params).all();
      let countQuery = "SELECT COUNT(*) as total FROM users";
      if (conditions.length > 0) {
        countQuery += " WHERE " + conditions.join(" AND ");
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      return success(c, "\u7528\u6237\u5217\u8868\u83B7\u53D6\u6210\u529F", {
        users: users.results,
        pagination: {
          page,
          limit,
          total: countResult?.total || 0,
          totalPages: Math.ceil((countResult?.total || 0) / limit)
        }
      });
    } catch (error2) {
      console.error("Get users error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.post("/api/users", async (c) => {
    try {
      const body = await c.req.json();
      const { username, password, role, display_name, contact_wechat, avatar_url } = body;
      if (!username || !password || !role) {
        return error(c, "\u7528\u6237\u540D\u3001\u5BC6\u7801\u548C\u89D2\u8272\u4E0D\u80FD\u4E3A\u7A7A", 400);
      }
      if (!["admin", "distributor"].includes(role)) {
        return error(c, "\u89D2\u8272\u5FC5\u987B\u662F admin \u6216 distributor", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM users WHERE username = ?").bind(username).first();
      if (existing) {
        return error(c, "\u7528\u6237\u540D\u5DF2\u5B58\u5728", 400);
      }
      const passwordHash = await hash(password, 10);
      const result = await c.env.DB.prepare(`
        INSERT INTO users (username, password_hash, role, status, display_name, contact_wechat, avatar_url, created_at, updated_at)
        VALUES (?, ?, ?, 'active', ?, ?, ?, datetime('now'), datetime('now'))
      `).bind(username, passwordHash, role, display_name || null, contact_wechat || null, avatar_url || null).run();
      return success(c, "\u7528\u6237\u521B\u5EFA\u6210\u529F", { id: result.meta.last_row_id });
    } catch (error2) {
      console.error("Create user error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.get("/api/users/:id", async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u7528\u6237ID", 400);
      }
      const user = await c.env.DB.prepare(`
        SELECT id, username, role, status, display_name, contact_wechat, avatar_url, created_at, updated_at 
        FROM users 
        WHERE id = ?
      `).bind(id).first();
      if (!user) {
        return error(c, "\u7528\u6237\u4E0D\u5B58\u5728", 404);
      }
      let authorizedVersions = [];
      if (user.role === "distributor") {
        const authorizations = await c.env.DB.prepare(`
          SELECT da.version_id, da.custom_price, pv.version, pv.version_name, pv.default_price,
                 p.id as product_id, p.name as product_name
          FROM distributor_authorizations da
          JOIN product_versions pv ON da.version_id = pv.id
          JOIN products p ON pv.product_id = p.id
          WHERE da.distributor_id = ? AND da.status = 'active'
          ORDER BY p.name, pv.version
        `).bind(id).all();
        authorizedVersions = authorizations.results;
      }
      return success(c, "\u7528\u6237\u4FE1\u606F\u83B7\u53D6\u6210\u529F", {
        user: {
          ...user,
          authorized_versions: authorizedVersions
        }
      });
    } catch (error2) {
      console.error("Get user error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.put("/api/users/:id", async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      const updateData = await c.req.json();
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u7528\u6237ID", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM users WHERE id = ?").bind(id).first();
      if (!existing) {
        return error(c, "\u7528\u6237\u4E0D\u5B58\u5728", 404);
      }
      const updateFields = [];
      const params = [];
      if (updateData.password !== void 0) {
        const passwordHash = await hash(updateData.password, 10);
        updateFields.push("password_hash = ?");
        params.push(passwordHash);
      }
      if (updateData.role !== void 0) {
        if (!["admin", "distributor"].includes(updateData.role)) {
          return error(c, "\u89D2\u8272\u5FC5\u987B\u662F admin \u6216 distributor", 400);
        }
        updateFields.push("role = ?");
        params.push(updateData.role);
      }
      if (updateData.status !== void 0) {
        updateFields.push("status = ?");
        params.push(updateData.status);
      }
      if (updateData.display_name !== void 0) {
        updateFields.push("display_name = ?");
        params.push(updateData.display_name);
      }
      if (updateData.contact_wechat !== void 0) {
        updateFields.push("contact_wechat = ?");
        params.push(updateData.contact_wechat);
      }
      if (updateData.avatar_url !== void 0) {
        updateFields.push("avatar_url = ?");
        params.push(updateData.avatar_url);
      }
      if (updateFields.length === 0) {
        return error(c, "\u6CA1\u6709\u8981\u66F4\u65B0\u7684\u5B57\u6BB5", 400);
      }
      updateFields.push("updated_at = datetime('now')");
      params.push(id);
      const query = `UPDATE users SET ${updateFields.join(", ")} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      const cacheKey = `user:auth:${id}`;
      await c.env.CACHE.delete(cacheKey);
      return success(c, "\u7528\u6237\u66F4\u65B0\u6210\u529F");
    } catch (error2) {
      console.error("Update user error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.delete("/api/users/:id", async (c) => {
    try {
      const currentUser = c.get("admin");
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u7528\u6237ID", 400);
      }
      if (currentUser.admin_id === id) {
        return error(c, "\u4E0D\u80FD\u5220\u9664\u81EA\u5DF1\u7684\u8D26\u53F7", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id, role FROM users WHERE id = ?").bind(id).first();
      if (!existing) {
        return error(c, "\u7528\u6237\u4E0D\u5B58\u5728", 404);
      }
      const activeLicenses = await c.env.DB.prepare("SELECT COUNT(*) as count FROM licenses WHERE admin_id = ? AND status = 'active'").bind(id).first();
      if (activeLicenses && activeLicenses.count > 0) {
        return error(c, "\u4E0D\u80FD\u5220\u9664\u62E5\u6709\u6D3B\u8DC3\u8BB8\u53EF\u8BC1\u7684\u7528\u6237", 409);
      }
      if (existing.role === "distributor") {
        await c.env.DB.prepare("DELETE FROM distributor_authorizations WHERE distributor_id = ?").bind(id).run();
      }
      await c.env.DB.prepare("DELETE FROM users WHERE id = ?").bind(id).run();
      const cacheKey = `user:auth:${id}`;
      await c.env.CACHE.delete(cacheKey);
      return success(c, "\u7528\u6237\u5220\u9664\u6210\u529F");
    } catch (error2) {
      console.error("Delete user error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
}
__name(setupUserRoutes, "setupUserRoutes");

// src/routes/licenses.ts
init_modules_watch_stub();
function setupLicenseRoutes(app2) {
  app2.get("/api/v1/licenses", authMiddleware(), async (c) => {
    try {
      const user = c.get("admin");
      const query = c.req.query();
      const page = parseInt(query.page || "1");
      const limit = parseInt(query.limit || "20");
      const version_id = query.version_id ? parseInt(query.version_id) : void 0;
      const status = query.status;
      const search = query.search;
      const offset = (page - 1) * limit;
      let queryStr = `
        SELECT l.*, pv.version, pv.version_name, p.name as product_name 
        FROM licenses l 
        JOIN product_versions pv ON l.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
      `;
      const params = [];
      const conditions = [];
      if (user.role === "distributor") {
        conditions.push("l.admin_id = ?");
        params.push(user.admin_id);
      }
      if (version_id) {
        conditions.push("l.version_id = ?");
        params.push(version_id);
      }
      if (status) {
        conditions.push("l.status = ?");
        params.push(status);
      }
      if (search) {
        conditions.push("l.license_key LIKE ?");
        params.push(`%${search}%`);
      }
      if (conditions.length > 0) {
        queryStr += " WHERE " + conditions.join(" AND ");
      }
      queryStr += " ORDER BY l.created_at DESC LIMIT ? OFFSET ?";
      params.push(limit, offset);
      const licenses = await c.env.DB.prepare(queryStr).bind(...params).all();
      let countQuery = "SELECT COUNT(*) as total FROM licenses l";
      if (conditions.length > 0) {
        countQuery += " WHERE " + conditions.join(" AND ");
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      return success(c, "\u8BB8\u53EF\u8BC1\u5217\u8868\u83B7\u53D6\u6210\u529F", {
        licenses: licenses.results,
        pagination: {
          page,
          limit,
          total: countResult?.total || 0,
          totalPages: Math.ceil((countResult?.total || 0) / limit)
        }
      });
    } catch (error2) {
      console.error("Get licenses error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
  app2.post("/api/v1/licenses", authMiddleware(), async (c) => {
    try {
      const user = c.get("admin");
      const body = await c.req.json();
      const { version_id, count = 1, expires_at, max_devices } = body;
      if (!version_id) {
        return error(c, "\u7248\u672CID\u4E0D\u80FD\u4E3A\u7A7A", 400);
      }
      if (count <= 0 || count > 100) {
        return error(c, "\u6570\u91CF\u5FC5\u987B\u57281\u5230100\u4E4B\u95F4", 400);
      }
      const version = await c.env.DB.prepare(`
        SELECT pv.*, p.name as product_name 
        FROM product_versions pv
        JOIN products p ON pv.product_id = p.id
        WHERE pv.id = ? AND pv.status = 'active' AND p.status = 'active'
      `).bind(version_id).first();
      if (!version) {
        return error(c, "\u4EA7\u54C1\u7248\u672C\u4E0D\u5B58\u5728\u6216\u5DF2\u505C\u7528", 400);
      }
      if (user.role === "distributor") {
        const auth = await c.env.DB.prepare(`
          SELECT * FROM distributor_authorizations 
          WHERE distributor_id = ? AND version_id = ? AND status = 'active'
        `).bind(user.admin_id, version_id).first();
        if (!auth) {
          return error(c, "\u60A8\u6CA1\u6709\u6743\u9650\u9500\u552E\u6B64\u4EA7\u54C1\u7248\u672C", 403);
        }
      }
      let unitPrice = version.default_price;
      if (user.role === "distributor") {
        const pricing = await c.env.DB.prepare(`
          SELECT custom_price FROM distributor_authorizations 
          WHERE distributor_id = ? AND version_id = ? AND status = 'active'
        `).bind(user.admin_id, version_id).first();
        if (pricing?.custom_price) {
          unitPrice = pricing.custom_price;
        }
      }
      const totalPrice = unitPrice * count;
      try {
        const orderResult = await c.env.DB.prepare(`
          INSERT INTO orders (admin_id, version_id, license_count, unit_price, total_price, status, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, 'completed', datetime('now'), datetime('now'))
        `).bind(user.admin_id, version_id, count, unitPrice, totalPrice).run();
        const orderId = orderResult.meta.last_row_id;
        const licenses = [];
        const insertPromises = [];
        for (let i = 0; i < count; i++) {
          const licenseKey = generateRandomKey(24);
          licenses.push(licenseKey);
          const finalMaxDevices = max_devices || version.max_devices;
          insertPromises.push(
            c.env.DB.prepare(`
              INSERT INTO licenses (version_id, license_key, status, expires_at, max_devices, admin_id, order_id, created_at, updated_at)
              VALUES (?, ?, 'active', ?, ?, ?, ?, datetime('now'), datetime('now'))
            `).bind(version_id, licenseKey, expires_at || null, finalMaxDevices, user.admin_id, orderId).run()
          );
        }
        await Promise.all(insertPromises);
        return success(c, `\u6210\u529F\u751F\u6210${count}\u4E2A\u8BB8\u53EF\u8BC1`, {
          licenses,
          order_id: orderId,
          total_price: totalPrice
        });
      } catch (error2) {
        console.error("License creation transaction error:", error2);
        return error(c, "\u8BB8\u53EF\u8BC1\u751F\u6210\u5931\u8D25", 500);
      }
    } catch (error2) {
      console.error("Create licenses error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
  app2.get("/api/v1/licenses/:id", authMiddleware(), async (c) => {
    try {
      const user = c.get("admin");
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u8BB8\u53EF\u8BC1ID", 400);
      }
      const license = await c.env.DB.prepare(`
        SELECT l.*, pv.version, pv.version_name, pv.verification_strategy,
               p.name as product_name, o.id as order_id, o.total_price
        FROM licenses l
        JOIN product_versions pv ON l.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        LEFT JOIN orders o ON l.order_id = o.id
        WHERE l.id = ?
      `).bind(id).first();
      if (!license) {
        return error(c, "License not found", 404);
      }
      if (user.role === "distributor" && license.admin_id !== user.admin_id) {
        return error(c, "Access denied to this license", 403);
      }
      const devices = await c.env.DB.prepare("SELECT * FROM devices WHERE license_id = ?").bind(id).all();
      const logs = await c.env.DB.prepare(`
        SELECT * FROM verification_logs 
        WHERE license_key = ? 
        ORDER BY created_at DESC 
        LIMIT 50
      `).bind(license.license_key).all();
      return success(c, "\u8BB8\u53EF\u8BC1\u4FE1\u606F\u83B7\u53D6\u6210\u529F", {
        license,
        devices: devices.results,
        verification_logs: logs.results
      });
    } catch (error2) {
      console.error("Get license error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
  app2.put("/api/v1/licenses/:id", authMiddleware(), async (c) => {
    try {
      const user = c.get("admin");
      const id = parseInt(c.req.param("id"));
      const updateData = await c.req.json();
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u8BB8\u53EF\u8BC1ID", 400);
      }
      const license = await c.env.DB.prepare(`
        SELECT l.*, pv.id as version_id
        FROM licenses l
        JOIN product_versions pv ON l.version_id = pv.id
        WHERE l.id = ?
      `).bind(id).first();
      if (!license) {
        return error(c, "License not found", 404);
      }
      if (user.role === "distributor" && license.admin_id !== user.admin_id) {
        return error(c, "Access denied to this license", 403);
      }
      const updateFields = [];
      const params = [];
      if (updateData.status !== void 0) {
        updateFields.push("status = ?");
        params.push(updateData.status);
      }
      if (updateData.expires_at !== void 0) {
        updateFields.push("expires_at = ?");
        params.push(updateData.expires_at);
      }
      if (updateData.max_devices !== void 0) {
        updateFields.push("max_devices = ?");
        params.push(updateData.max_devices);
      }
      if (updateFields.length === 0) {
        return error(c, "\u6CA1\u6709\u8981\u66F4\u65B0\u7684\u5B57\u6BB5", 400);
      }
      updateFields.push("updated_at = datetime('now')");
      params.push(id);
      const query = `UPDATE licenses SET ${updateFields.join(", ")} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      const cacheKey = `license:verify:${license.license_key}`;
      await c.env.CACHE.delete(cacheKey);
      return success(c, "\u8BB8\u53EF\u8BC1\u66F4\u65B0\u6210\u529F");
    } catch (error2) {
      console.error("Update license error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
}
__name(setupLicenseRoutes, "setupLicenseRoutes");

// src/routes/client.ts
init_modules_watch_stub();
function setupClientRoutes(app2) {
  app2.post("/verify", async (c) => {
    try {
      const body = await c.req.json();
      const { license_key, device_id, product_features } = body;
      if (!license_key) {
        return error(c, "\u8BB8\u53EF\u8BC1\u5BC6\u94A5\u4E3A\u5FC5\u586B\u9879", 400);
      }
      const cacheKey = `license:verify:${license_key}`;
      const cached = await c.env.CACHE.get(cacheKey);
      if (cached) {
        const cachedData = JSON.parse(cached);
        return success(c, "\u8BB8\u53EF\u8BC1\u9A8C\u8BC1\u6210\u529F(\u7F13\u5B58)", {
          license_info: cachedData
        });
      }
      const license = await c.env.DB.prepare(`
        SELECT l.*, p.name as product_name, p.verification_strategy, p.max_devices as product_max_devices, p.features
        FROM licenses l
        JOIN products p ON l.product_id = p.id
        WHERE l.license_key = ? AND l.status = 'active' AND p.status = 'active'
      `).bind(license_key).first();
      if (!license) {
        await c.env.DB.prepare(`
          INSERT INTO verification_logs (license_key, device_id, result, reason, ip_address, user_agent, created_at)
          VALUES (?, ?, 'failed', 'Invalid or inactive license', ?, ?, datetime('now'))
        `).bind(
          license_key,
          device_id || null,
          c.req.header("CF-Connecting-IP") || c.req.header("X-Forwarded-For") || "",
          c.req.header("User-Agent") || ""
        ).run();
        return error(c, "\u65E0\u6548\u6216\u5DF2\u505C\u7528\u7684\u8BB8\u53EF\u8BC1\u5BC6\u94A5", 400);
      }
      if (license.expires_at && new Date(license.expires_at) < /* @__PURE__ */ new Date()) {
        return error(c, "\u8BB8\u53EF\u8BC1\u5DF2\u8FC7\u671F", 400);
      }
      let currentDevices = 0;
      if (license.verification_strategy === "device_count" && device_id) {
        const deviceCount = await c.env.DB.prepare(`
          SELECT COUNT(*) as count FROM devices WHERE license_id = ?
        `).bind(license.id).first();
        currentDevices = deviceCount?.count || 0;
        const maxDevices = license.max_devices || license.product_max_devices || 1;
        const existingDevice = await c.env.DB.prepare(`
          SELECT id FROM devices WHERE license_id = ? AND device_id = ?
        `).bind(license.id, device_id).first();
        if (!existingDevice && currentDevices >= maxDevices) {
          return error(c, `\u8BBE\u5907\u6570\u91CF\u8D85\u9650\uFF0C\u6700\u591A\u5141\u8BB8${maxDevices}\u53F0\u8BBE\u5907`, 400);
        }
        if (!existingDevice) {
          await c.env.DB.prepare(`
            INSERT INTO devices (license_id, device_id, device_info, last_verification, created_at, updated_at)
            VALUES (?, ?, ?, datetime('now'), datetime('now'), datetime('now'))
          `).bind(license.id, device_id, JSON.stringify({ user_agent: c.req.header("User-Agent") })).run();
          currentDevices++;
        } else {
          await c.env.DB.prepare(`
            UPDATE devices SET last_verification = datetime('now'), updated_at = datetime('now')
            WHERE license_id = ? AND device_id = ?
          `).bind(license.id, device_id).run();
        }
      }
      let features = [];
      if (license.features) {
        try {
          features = JSON.parse(license.features);
        } catch {
          features = license.features.split(",").map((f) => f.trim());
        }
      }
      const licenseInfo = {
        product_name: license.product_name,
        expires_at: license.expires_at,
        max_devices: license.max_devices || license.product_max_devices,
        current_devices: currentDevices,
        features
      };
      await c.env.CACHE.put(cacheKey, JSON.stringify(licenseInfo), { expirationTtl: 300 });
      await c.env.DB.prepare(`
        INSERT INTO verification_logs (license_key, device_id, result, reason, ip_address, user_agent, created_at)
        VALUES (?, ?, 'success', 'License verified successfully', ?, ?, datetime('now'))
      `).bind(
        license_key,
        device_id || null,
        c.req.header("CF-Connecting-IP") || c.req.header("X-Forwarded-For") || "",
        c.req.header("User-Agent") || ""
      ).run();
      return success(c, "\u8BB8\u53EF\u8BC1\u9A8C\u8BC1\u6210\u529F", {
        license_info: licenseInfo
      });
    } catch (error2) {
      console.error("License verification error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.post("/device/unbind", async (c) => {
    try {
      const body = await c.req.json();
      const { license_key, device_id } = body;
      if (!license_key || !device_id) {
        return error(c, "\u8BB8\u53EF\u8BC1\u5BC6\u94A5\u548C\u8BBE\u5907ID\u4E3A\u5FC5\u586B\u9879", 400);
      }
      const license = await c.env.DB.prepare(`
        SELECT id FROM licenses WHERE license_key = ? AND status = 'active'
      `).bind(license_key).first();
      if (!license) {
        return error(c, "\u65E0\u6548\u6216\u5DF2\u505C\u7528\u7684\u8BB8\u53EF\u8BC1\u5BC6\u94A5", 400);
      }
      const result = await c.env.DB.prepare(`
        DELETE FROM devices WHERE license_id = ? AND device_id = ?
      `).bind(license.id, device_id).run();
      if (result.meta.changes === 0) {
        return error(c, "\u672A\u627E\u5230\u8BBE\u5907\u7ED1\u5B9A", 400);
      }
      const cacheKey = `license:verify:${license_key}`;
      await c.env.CACHE.delete(cacheKey);
      return success(c, "\u8BBE\u5907\u89E3\u7ED1\u6210\u529F");
    } catch (error2) {
      console.error("Device unbind error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
}
__name(setupClientRoutes, "setupClientRoutes");

// src/routes/orders.ts
init_modules_watch_stub();
function setupOrderRoutes(app2) {
  app2.get("/api/orders", authMiddleware(), async (c) => {
    try {
      const admin = c.get("admin");
      const query = c.req.query();
      const page = parseInt(query.page || "1");
      const limit = parseInt(query.limit || "20");
      const status = query.status;
      const product_id = query.product_id ? parseInt(query.product_id) : void 0;
      const start_date = query.start_date;
      const end_date = query.end_date;
      const offset = (page - 1) * limit;
      let queryStr = `
        SELECT o.*, p.name as product_name, a.username as admin_username
        FROM orders o
        JOIN products p ON o.product_id = p.id
        JOIN admins a ON o.admin_id = a.id
      `;
      const params = [];
      const conditions = [];
      if (admin.role !== "super") {
        conditions.push("o.admin_id = ?");
        params.push(admin.admin_id);
      }
      if (status) {
        conditions.push("o.status = ?");
        params.push(status);
      }
      if (product_id) {
        conditions.push("o.product_id = ?");
        params.push(product_id);
      }
      if (start_date) {
        conditions.push("o.created_at >= ?");
        params.push(start_date);
      }
      if (end_date) {
        conditions.push("o.created_at <= ?");
        params.push(end_date);
      }
      if (conditions.length > 0) {
        queryStr += " WHERE " + conditions.join(" AND ");
      }
      queryStr += " ORDER BY o.created_at DESC LIMIT ? OFFSET ?";
      params.push(limit, offset);
      const orders = await c.env.DB.prepare(queryStr).bind(...params).all();
      let countQuery = "SELECT COUNT(*) as total FROM orders o";
      if (conditions.length > 0) {
        countQuery += " WHERE " + conditions.join(" AND ");
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      return success(c, "\u8BA2\u5355\u5217\u8868\u83B7\u53D6\u6210\u529F", {
        orders: orders.results,
        pagination: {
          page,
          limit,
          total: countResult?.total || 0,
          totalPages: Math.ceil((countResult?.total || 0) / limit)
        }
      });
    } catch (error2) {
      console.error("Get orders error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
  app2.post("/api/orders", authMiddleware(), async (c) => {
    try {
      const admin = c.get("admin");
      const body = await c.req.json();
      const { product_id, license_count, unit_price } = body;
      if (!product_id || !license_count || !unit_price) {
        return error(c, "\u4EA7\u54C1ID\u3001\u8BB8\u53EF\u8BC1\u6570\u91CF\u548C\u5355\u4EF7\u4E0D\u80FD\u4E3A\u7A7A", 400);
      }
      const product = await c.env.DB.prepare("SELECT * FROM products WHERE id = ? AND status = 'active'").bind(product_id).first();
      if (!product) {
        return error(c, "Product not found or inactive", 400);
      }
      if (admin.role !== "super") {
        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);
        if (adminInfo) {
          const { product_ids } = JSON.parse(adminInfo);
          if (!product_ids || !product_ids.includes(product_id.toString())) {
            return error(c, "Access denied to this product", 403);
          }
        }
      }
      const totalPrice = unit_price * license_count;
      const result = await c.env.DB.prepare(`
        INSERT INTO orders (admin_id, product_id, license_count, unit_price, total_price, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 'pending', datetime('now'), datetime('now'))
      `).bind(admin.admin_id, product_id, license_count, unit_price, totalPrice).run();
      return success(c, "\u8BA2\u5355\u521B\u5EFA\u6210\u529F", {
        id: result.meta.last_row_id,
        total_price: totalPrice
      });
    } catch (error2) {
      console.error("Create order error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
  app2.get("/api/orders/:id", authMiddleware(), async (c) => {
    try {
      const admin = c.get("admin");
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u8BA2\u5355ID", 400);
      }
      let queryStr = `
        SELECT o.*, p.name as product_name, a.username as admin_username
        FROM orders o
        JOIN products p ON o.product_id = p.id
        JOIN admins a ON o.admin_id = a.id
        WHERE o.id = ?
      `;
      if (admin.role !== "super") {
        queryStr += " AND o.admin_id = ?";
      }
      const params = admin.role === "super" ? [id] : [id, admin.admin_id];
      const order = await c.env.DB.prepare(queryStr).bind(...params).first();
      if (!order) {
        return error(c, "Order not found or access denied", 404);
      }
      return success(c, "\u8BA2\u5355\u4FE1\u606F\u83B7\u53D6\u6210\u529F", { order });
    } catch (error2) {
      console.error("Get order error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
  app2.put("/api/orders/:id", authMiddleware(), async (c) => {
    try {
      const admin = c.get("admin");
      const id = parseInt(c.req.param("id"));
      const body = await c.req.json();
      const { status } = body;
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u8BA2\u5355ID", 400);
      }
      if (!status) {
        return error(c, "\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A", 400);
      }
      let checkQuery = "SELECT * FROM orders WHERE id = ?";
      if (admin.role !== "super") {
        checkQuery += " AND admin_id = ?";
      }
      const params = admin.role === "super" ? [id] : [id, admin.admin_id];
      const order = await c.env.DB.prepare(checkQuery).bind(...params).first();
      if (!order) {
        return error(c, "Order not found or access denied", 404);
      }
      await c.env.DB.prepare(`
        UPDATE orders SET status = ?, updated_at = datetime('now') WHERE id = ?
      `).bind(status, id).run();
      return success(c, "\u8BA2\u5355\u72B6\u6001\u66F4\u65B0\u6210\u529F");
    } catch (error2) {
      console.error("Update order error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
  app2.delete("/api/orders/:id", authMiddleware(), async (c) => {
    try {
      const admin = c.get("admin");
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u8BA2\u5355ID", 400);
      }
      let checkQuery = "SELECT * FROM orders WHERE id = ?";
      if (admin.role !== "super") {
        checkQuery += " AND admin_id = ?";
      }
      const params = admin.role === "super" ? [id] : [id, admin.admin_id];
      const order = await c.env.DB.prepare(checkQuery).bind(...params).first();
      if (!order) {
        return error(c, "Order not found or access denied", 404);
      }
      if (order.status !== "pending") {
        return error(c, "Cannot delete completed or cancelled order", 409);
      }
      await c.env.DB.prepare("DELETE FROM orders WHERE id = ?").bind(id).run();
      return success(c, "\u8BA2\u5355\u5220\u9664\u6210\u529F");
    } catch (error2) {
      console.error("Delete order error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
}
__name(setupOrderRoutes, "setupOrderRoutes");

// src/routes/products.ts
init_modules_watch_stub();
function setupProductRoutes(app2) {
  app2.get("/api/products", authMiddleware(), async (c) => {
    try {
      const query = c.req.query();
      const page = parseInt(query.page || "1");
      const limit = parseInt(query.limit || "20");
      const status = query.status;
      const search = query.search;
      const offset = (page - 1) * limit;
      let queryStr = `
        SELECT p.*, 
               COUNT(pv.id) as version_count,
               COUNT(CASE WHEN pv.status = 'active' THEN 1 END) as active_versions
        FROM products p 
        LEFT JOIN product_versions pv ON p.id = pv.product_id
      `;
      const params = [];
      const conditions = [];
      if (status) {
        conditions.push("p.status = ?");
        params.push(status);
      }
      if (search) {
        conditions.push("(p.name LIKE ? OR p.description LIKE ?)");
        params.push(`%${search}%`, `%${search}%`);
      }
      if (conditions.length > 0) {
        queryStr += " WHERE " + conditions.join(" AND ");
      }
      queryStr += " GROUP BY p.id ORDER BY p.created_at DESC LIMIT ? OFFSET ?";
      params.push(limit, offset);
      const products = await c.env.DB.prepare(queryStr).bind(...params).all();
      let countQuery = "SELECT COUNT(*) as total FROM products p";
      if (conditions.length > 0) {
        countQuery += " WHERE " + conditions.join(" AND ");
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      return success(c, "\u4EA7\u54C1\u5217\u8868\u83B7\u53D6\u6210\u529F", {
        products: products.results,
        pagination: {
          page,
          limit,
          total: countResult?.total || 0,
          totalPages: Math.ceil((countResult?.total || 0) / limit)
        }
      });
    } catch (error2) {
      console.error("Get products error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.post("/api/products", authMiddleware(), adminOnly(), async (c) => {
    try {
      const body = await c.req.json();
      const { name, description, category } = body;
      if (!name) {
        return error(c, "\u4EA7\u54C1\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM products WHERE name = ?").bind(name).first();
      if (existing) {
        return error(c, "\u4EA7\u54C1\u540D\u79F0\u5DF2\u5B58\u5728", 400);
      }
      const result = await c.env.DB.prepare(`
        INSERT INTO products (name, description, category, status, created_at, updated_at)
        VALUES (?, ?, ?, 'active', datetime('now'), datetime('now'))
      `).bind(name, description || null, category || null).run();
      return success(c, "\u4EA7\u54C1\u521B\u5EFA\u6210\u529F", { id: result.meta.last_row_id });
    } catch (error2) {
      console.error("Create product error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.get("/api/products/:id", authMiddleware(), async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u4EA7\u54C1ID", 400);
      }
      const product = await c.env.DB.prepare("SELECT * FROM products WHERE id = ?").bind(id).first();
      if (!product) {
        return error(c, "\u4EA7\u54C1\u4E0D\u5B58\u5728", 404);
      }
      const versions = await c.env.DB.prepare(`
        SELECT * FROM product_versions 
        WHERE product_id = ? 
        ORDER BY version DESC
      `).bind(id).all();
      return success(c, "\u4EA7\u54C1\u4FE1\u606F\u83B7\u53D6\u6210\u529F", {
        product: {
          ...product,
          versions: versions.results
        }
      });
    } catch (error2) {
      console.error("Get product error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.put("/api/products/:id", authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      const updateData = await c.req.json();
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u4EA7\u54C1ID", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM products WHERE id = ?").bind(id).first();
      if (!existing) {
        return error(c, "\u4EA7\u54C1\u4E0D\u5B58\u5728", 404);
      }
      const updateFields = [];
      const params = [];
      if (updateData.name !== void 0) {
        updateFields.push("name = ?");
        params.push(updateData.name);
      }
      if (updateData.description !== void 0) {
        updateFields.push("description = ?");
        params.push(updateData.description);
      }
      if (updateData.category !== void 0) {
        updateFields.push("category = ?");
        params.push(updateData.category);
      }
      if (updateData.status !== void 0) {
        updateFields.push("status = ?");
        params.push(updateData.status);
      }
      if (updateFields.length === 0) {
        return error(c, "\u6CA1\u6709\u8981\u66F4\u65B0\u7684\u5B57\u6BB5", 400);
      }
      updateFields.push("updated_at = datetime('now')");
      params.push(id);
      const query = `UPDATE products SET ${updateFields.join(", ")} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      return success(c, "\u4EA7\u54C1\u66F4\u65B0\u6210\u529F");
    } catch (error2) {
      console.error("Update product error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.delete("/api/products/:id", authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u4EA7\u54C1ID", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM products WHERE id = ?").bind(id).first();
      if (!existing) {
        return error(c, "\u4EA7\u54C1\u4E0D\u5B58\u5728", 404);
      }
      const activeLicenses = await c.env.DB.prepare(`
        SELECT COUNT(*) as count 
        FROM licenses l 
        JOIN product_versions pv ON l.version_id = pv.id 
        WHERE pv.product_id = ? AND l.status = 'active'
      `).bind(id).first();
      if (activeLicenses && activeLicenses.count > 0) {
        return error(c, "\u4E0D\u80FD\u5220\u9664\u62E5\u6709\u6D3B\u8DC3\u8BB8\u53EF\u8BC1\u7684\u4EA7\u54C1", 409);
      }
      await c.env.DB.prepare("DELETE FROM distributor_authorizations WHERE version_id IN (SELECT id FROM product_versions WHERE product_id = ?)").bind(id).run();
      await c.env.DB.prepare("DELETE FROM product_versions WHERE product_id = ?").bind(id).run();
      await c.env.DB.prepare("DELETE FROM products WHERE id = ?").bind(id).run();
      return success(c, "\u4EA7\u54C1\u5220\u9664\u6210\u529F");
    } catch (error2) {
      console.error("Delete product error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.get("/api/products/:id/versions", authMiddleware(), async (c) => {
    try {
      const productId = parseInt(c.req.param("id"));
      if (!productId || productId <= 0) {
        return error(c, "\u65E0\u6548\u7684\u4EA7\u54C1ID", 400);
      }
      const product = await c.env.DB.prepare("SELECT id, name FROM products WHERE id = ?").bind(productId).first();
      if (!product) {
        return error(c, "\u4EA7\u54C1\u4E0D\u5B58\u5728", 404);
      }
      const versions = await c.env.DB.prepare(`
        SELECT pv.*, COUNT(l.id) as license_count
        FROM product_versions pv
        LEFT JOIN licenses l ON pv.id = l.version_id
        WHERE pv.product_id = ?
        GROUP BY pv.id
        ORDER BY pv.version DESC
      `).bind(productId).all();
      return success(c, "\u4EA7\u54C1\u7248\u672C\u5217\u8868\u83B7\u53D6\u6210\u529F", {
        product,
        versions: versions.results
      });
    } catch (error2) {
      console.error("Get product versions error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.post("/api/products/:id/versions", authMiddleware(), adminOnly(), async (c) => {
    try {
      const productId = parseInt(c.req.param("id"));
      const body = await c.req.json();
      const {
        version,
        version_name,
        description,
        features,
        verification_strategy,
        max_devices,
        default_price,
        download_link,
        changelog
      } = body;
      if (!productId || productId <= 0) {
        return error(c, "\u65E0\u6548\u7684\u4EA7\u54C1ID", 400);
      }
      if (!version || !verification_strategy || !default_price) {
        return error(c, "\u7248\u672C\u53F7\u3001\u9A8C\u8BC1\u7B56\u7565\u548C\u9ED8\u8BA4\u4EF7\u683C\u4E0D\u80FD\u4E3A\u7A7A", 400);
      }
      const product = await c.env.DB.prepare("SELECT id FROM products WHERE id = ? AND status = 'active'").bind(productId).first();
      if (!product) {
        return error(c, "\u4EA7\u54C1\u4E0D\u5B58\u5728\u6216\u5DF2\u505C\u7528", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM product_versions WHERE product_id = ? AND version = ?").bind(productId, version).first();
      if (existing) {
        return error(c, "\u8BE5\u7248\u672C\u53F7\u5DF2\u5B58\u5728", 400);
      }
      const featuresJson = features ? JSON.stringify(features) : null;
      const result = await c.env.DB.prepare(`
        INSERT INTO product_versions 
        (product_id, version, version_name, description, features, verification_strategy, max_devices, default_price, download_link, changelog, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', datetime('now'), datetime('now'))
      `).bind(
        productId,
        version,
        version_name || null,
        description || null,
        featuresJson,
        verification_strategy,
        max_devices || null,
        default_price,
        download_link || null,
        changelog || null
      ).run();
      return success(c, "\u4EA7\u54C1\u7248\u672C\u521B\u5EFA\u6210\u529F", { id: result.meta.last_row_id });
    } catch (error2) {
      console.error("Create product version error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.get("/api/versions/:id", authMiddleware(), async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u7248\u672CID", 400);
      }
      const version = await c.env.DB.prepare(`
        SELECT pv.*, p.name as product_name
        FROM product_versions pv
        JOIN products p ON pv.product_id = p.id
        WHERE pv.id = ?
      `).bind(id).first();
      if (!version) {
        return error(c, "\u4EA7\u54C1\u7248\u672C\u4E0D\u5B58\u5728", 404);
      }
      return success(c, "\u4EA7\u54C1\u7248\u672C\u4FE1\u606F\u83B7\u53D6\u6210\u529F", { version });
    } catch (error2) {
      console.error("Get product version error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.put("/api/versions/:id", authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      const updateData = await c.req.json();
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u7248\u672CID", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM product_versions WHERE id = ?").bind(id).first();
      if (!existing) {
        return error(c, "\u4EA7\u54C1\u7248\u672C\u4E0D\u5B58\u5728", 404);
      }
      const updateFields = [];
      const params = [];
      if (updateData.version !== void 0) {
        updateFields.push("version = ?");
        params.push(updateData.version);
      }
      if (updateData.version_name !== void 0) {
        updateFields.push("version_name = ?");
        params.push(updateData.version_name);
      }
      if (updateData.description !== void 0) {
        updateFields.push("description = ?");
        params.push(updateData.description);
      }
      if (updateData.features !== void 0) {
        updateFields.push("features = ?");
        params.push(updateData.features ? JSON.stringify(updateData.features) : null);
      }
      if (updateData.verification_strategy !== void 0) {
        updateFields.push("verification_strategy = ?");
        params.push(updateData.verification_strategy);
      }
      if (updateData.max_devices !== void 0) {
        updateFields.push("max_devices = ?");
        params.push(updateData.max_devices);
      }
      if (updateData.default_price !== void 0) {
        updateFields.push("default_price = ?");
        params.push(updateData.default_price);
      }
      if (updateData.download_link !== void 0) {
        updateFields.push("download_link = ?");
        params.push(updateData.download_link);
      }
      if (updateData.changelog !== void 0) {
        updateFields.push("changelog = ?");
        params.push(updateData.changelog);
      }
      if (updateData.status !== void 0) {
        updateFields.push("status = ?");
        params.push(updateData.status);
      }
      if (updateFields.length === 0) {
        return error(c, "\u6CA1\u6709\u8981\u66F4\u65B0\u7684\u5B57\u6BB5", 400);
      }
      updateFields.push("updated_at = datetime('now')");
      params.push(id);
      const query = `UPDATE product_versions SET ${updateFields.join(", ")} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      return success(c, "\u4EA7\u54C1\u7248\u672C\u66F4\u65B0\u6210\u529F");
    } catch (error2) {
      console.error("Update product version error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.delete("/api/versions/:id", authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u7248\u672CID", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM product_versions WHERE id = ?").bind(id).first();
      if (!existing) {
        return error(c, "\u4EA7\u54C1\u7248\u672C\u4E0D\u5B58\u5728", 404);
      }
      const activeLicenses = await c.env.DB.prepare("SELECT COUNT(*) as count FROM licenses WHERE version_id = ? AND status = 'active'").bind(id).first();
      if (activeLicenses && activeLicenses.count > 0) {
        return error(c, "\u4E0D\u80FD\u5220\u9664\u62E5\u6709\u6D3B\u8DC3\u8BB8\u53EF\u8BC1\u7684\u4EA7\u54C1\u7248\u672C", 409);
      }
      await c.env.DB.prepare("DELETE FROM distributor_authorizations WHERE version_id = ?").bind(id).run();
      await c.env.DB.prepare("DELETE FROM product_versions WHERE id = ?").bind(id).run();
      return success(c, "\u4EA7\u54C1\u7248\u672C\u5220\u9664\u6210\u529F");
    } catch (error2) {
      console.error("Delete product version error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
}
__name(setupProductRoutes, "setupProductRoutes");

// src/routes/distributors.ts
init_modules_watch_stub();
function setupDistributorRoutes(app2) {
  app2.get("/api/distributor-authorizations", authMiddleware(), adminOnly(), async (c) => {
    try {
      const query = c.req.query();
      const page = parseInt(query.page || "1");
      const limit = parseInt(query.limit || "20");
      const distributorId = query.distributor_id;
      const versionId = query.version_id;
      const status = query.status;
      const offset = (page - 1) * limit;
      let queryStr = `
        SELECT da.*, 
               u.username as distributor_username, 
               u.display_name as distributor_name,
               pv.version, pv.version_name, pv.default_price,
               p.name as product_name
        FROM distributor_authorizations da
        JOIN users u ON da.distributor_id = u.id
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
      `;
      const params = [];
      const conditions = [];
      if (distributorId) {
        conditions.push("da.distributor_id = ?");
        params.push(distributorId);
      }
      if (versionId) {
        conditions.push("da.version_id = ?");
        params.push(versionId);
      }
      if (status) {
        conditions.push("da.status = ?");
        params.push(status);
      }
      if (conditions.length > 0) {
        queryStr += " WHERE " + conditions.join(" AND ");
      }
      queryStr += " ORDER BY da.created_at DESC LIMIT ? OFFSET ?";
      params.push(limit, offset);
      const authorizations = await c.env.DB.prepare(queryStr).bind(...params).all();
      let countQuery = `
        SELECT COUNT(*) as total 
        FROM distributor_authorizations da
        JOIN users u ON da.distributor_id = u.id
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
      `;
      if (conditions.length > 0) {
        countQuery += " WHERE " + conditions.join(" AND ");
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      return success(c, "\u5206\u53D1\u5546\u6388\u6743\u5217\u8868\u83B7\u53D6\u6210\u529F", {
        authorizations: authorizations.results,
        pagination: {
          page,
          limit,
          total: countResult?.total || 0,
          totalPages: Math.ceil((countResult?.total || 0) / limit)
        }
      });
    } catch (error2) {
      console.error("Get distributor authorizations error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.post("/api/distributor-authorizations", authMiddleware(), adminOnly(), async (c) => {
    try {
      const body = await c.req.json();
      const { distributor_id, version_id, custom_price } = body;
      if (!distributor_id || !version_id) {
        return error(c, "\u5206\u53D1\u5546ID\u548C\u7248\u672CID\u4E0D\u80FD\u4E3A\u7A7A", 400);
      }
      const distributor = await c.env.DB.prepare("SELECT id, role FROM users WHERE id = ? AND role = 'distributor' AND status = 'active'").bind(distributor_id).first();
      if (!distributor) {
        return error(c, "\u5206\u53D1\u5546\u4E0D\u5B58\u5728\u6216\u5DF2\u505C\u7528", 400);
      }
      const version = await c.env.DB.prepare("SELECT id, default_price FROM product_versions WHERE id = ? AND status = 'active'").bind(version_id).first();
      if (!version) {
        return error(c, "\u4EA7\u54C1\u7248\u672C\u4E0D\u5B58\u5728\u6216\u5DF2\u505C\u7528", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM distributor_authorizations WHERE distributor_id = ? AND version_id = ?").bind(distributor_id, version_id).first();
      if (existing) {
        return error(c, "\u8BE5\u5206\u53D1\u5546\u5DF2\u88AB\u6388\u6743\u9500\u552E\u6B64\u7248\u672C", 400);
      }
      const result = await c.env.DB.prepare(`
        INSERT INTO distributor_authorizations (distributor_id, version_id, custom_price, status, created_at, updated_at)
        VALUES (?, ?, ?, 'active', datetime('now'), datetime('now'))
      `).bind(distributor_id, version_id, custom_price || null).run();
      return success(c, "\u5206\u53D1\u5546\u6388\u6743\u521B\u5EFA\u6210\u529F", { id: result.meta.last_row_id });
    } catch (error2) {
      console.error("Create distributor authorization error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.put("/api/distributor-authorizations/:id", authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      const updateData = await c.req.json();
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u6388\u6743ID", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM distributor_authorizations WHERE id = ?").bind(id).first();
      if (!existing) {
        return error(c, "\u6388\u6743\u4E0D\u5B58\u5728", 404);
      }
      const updateFields = [];
      const params = [];
      if (updateData.custom_price !== void 0) {
        updateFields.push("custom_price = ?");
        params.push(updateData.custom_price);
      }
      if (updateData.status !== void 0) {
        updateFields.push("status = ?");
        params.push(updateData.status);
      }
      if (updateFields.length === 0) {
        return error(c, "\u6CA1\u6709\u8981\u66F4\u65B0\u7684\u5B57\u6BB5", 400);
      }
      updateFields.push("updated_at = datetime('now')");
      params.push(id);
      const query = `UPDATE distributor_authorizations SET ${updateFields.join(", ")} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      return success(c, "\u5206\u53D1\u5546\u6388\u6743\u66F4\u65B0\u6210\u529F");
    } catch (error2) {
      console.error("Update distributor authorization error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.delete("/api/distributor-authorizations/:id", authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param("id"));
      if (!id || id <= 0) {
        return error(c, "\u65E0\u6548\u7684\u6388\u6743ID", 400);
      }
      const existing = await c.env.DB.prepare("SELECT id FROM distributor_authorizations WHERE id = ?").bind(id).first();
      if (!existing) {
        return error(c, "\u6388\u6743\u4E0D\u5B58\u5728", 404);
      }
      await c.env.DB.prepare("DELETE FROM distributor_authorizations WHERE id = ?").bind(id).run();
      return success(c, "\u5206\u53D1\u5546\u6388\u6743\u5220\u9664\u6210\u529F");
    } catch (error2) {
      console.error("Delete distributor authorization error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.get("/api/distributors/:id/versions", authMiddleware(), async (c) => {
    try {
      const distributorId = parseInt(c.req.param("id"));
      const currentUser = c.get("admin");
      if (!distributorId || distributorId <= 0) {
        return error(c, "\u65E0\u6548\u7684\u5206\u53D1\u5546ID", 400);
      }
      if (currentUser.role !== "admin" && currentUser.admin_id !== distributorId) {
        return error(c, "\u6743\u9650\u4E0D\u8DB3", 403);
      }
      const distributor = await c.env.DB.prepare("SELECT id, username, display_name FROM users WHERE id = ? AND role = 'distributor'").bind(distributorId).first();
      if (!distributor) {
        return error(c, "\u5206\u53D1\u5546\u4E0D\u5B58\u5728", 404);
      }
      const versions = await c.env.DB.prepare(`
        SELECT da.id as authorization_id, da.custom_price, da.status as auth_status,
               pv.id as version_id, pv.version, pv.version_name, pv.description, 
               pv.default_price, pv.download_link, pv.changelog, pv.features,
               p.id as product_id, p.name as product_name, p.category
        FROM distributor_authorizations da
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        WHERE da.distributor_id = ? AND da.status = 'active'
        ORDER BY p.name, pv.version
      `).bind(distributorId).all();
      return success(c, "\u5206\u53D1\u5546\u6388\u6743\u7248\u672C\u83B7\u53D6\u6210\u529F", {
        distributor,
        authorized_versions: versions.results
      });
    } catch (error2) {
      console.error("Get distributor versions error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
  app2.get("/api/store/:username", async (c) => {
    try {
      const username = c.req.param("username");
      if (!username) {
        return error(c, "\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A", 400);
      }
      const distributor = await c.env.DB.prepare(`
        SELECT id, username, display_name, contact_wechat, avatar_url
        FROM users 
        WHERE username = ? AND role = 'distributor' AND status = 'active'
      `).bind(username).first();
      if (!distributor) {
        return error(c, "\u5E97\u94FA\u4E0D\u5B58\u5728", 404);
      }
      const products = await c.env.DB.prepare(`
        SELECT da.custom_price,
               pv.id as version_id, pv.version, pv.version_name, pv.description, 
               pv.default_price, pv.features, pv.changelog,
               p.id as product_id, p.name as product_name, p.category, p.description as product_description
        FROM distributor_authorizations da
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        WHERE da.distributor_id = ? AND da.status = 'active' AND pv.status = 'active' AND p.status = 'active'
        ORDER BY p.name, pv.version
      `).bind(distributor.id).all();
      const groupedProducts = {};
      products.results.forEach((item) => {
        if (!groupedProducts[item.product_id]) {
          groupedProducts[item.product_id] = {
            id: item.product_id,
            name: item.product_name,
            description: item.product_description,
            category: item.category,
            versions: []
          };
        }
        groupedProducts[item.product_id].versions.push({
          id: item.version_id,
          version: item.version,
          version_name: item.version_name,
          description: item.description,
          features: item.features ? JSON.parse(item.features) : [],
          changelog: item.changelog,
          price: item.custom_price || item.default_price,
          original_price: item.default_price
        });
      });
      return success(c, "\u5E97\u94FA\u4FE1\u606F\u83B7\u53D6\u6210\u529F", {
        store: {
          distributor: {
            username: distributor.username,
            display_name: distributor.display_name,
            contact_wechat: distributor.contact_wechat,
            avatar_url: distributor.avatar_url
          },
          products: Object.values(groupedProducts)
        }
      });
    } catch (error2) {
      console.error("Get store page error:", error2);
      return error(c, "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF", 500);
    }
  });
}
__name(setupDistributorRoutes, "setupDistributorRoutes");

// src/routes/statistics.ts
init_modules_watch_stub();
function setupStatisticsRoutes(app2) {
  app2.get("/api/stats/sales", authMiddleware(), async (c) => {
    try {
      const admin = c.get("admin");
      const query = c.req.query();
      const period = query.period || "month";
      const start_date = query.start_date;
      const end_date = query.end_date;
      let dateFilter = "";
      const params = [];
      if (start_date && end_date) {
        dateFilter = "AND o.created_at BETWEEN ? AND ?";
        params.push(start_date, end_date);
      } else {
        const now = /* @__PURE__ */ new Date();
        let startDate;
        switch (period) {
          case "day":
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
          case "week":
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1e3);
            break;
          case "month":
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          case "year":
            startDate = new Date(now.getFullYear(), 0, 1);
            break;
          default:
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }
        dateFilter = "AND o.created_at >= ?";
        params.push(startDate.toISOString());
      }
      const cacheKey = `stats:${admin.admin_id}:${period}:${start_date || ""}:${end_date || ""}`;
      const cached = await c.env.CACHE.get(cacheKey);
      if (cached) {
        return success(c, "\u9500\u552E\u7EDF\u8BA1\u83B7\u53D6\u6210\u529F(\u7F13\u5B58)", JSON.parse(cached));
      }
      let adminFilter = "";
      if (admin.role !== "super") {
        adminFilter = "WHERE o.admin_id = ?";
        params.unshift(admin.admin_id);
      }
      const totalSalesQuery = `
        SELECT 
          COUNT(*) as total_orders,
          SUM(license_count) as total_licenses,
          SUM(total_price) as total_revenue
        FROM orders o
        ${adminFilter}
        ${adminFilter ? "AND" : "WHERE"} o.status = 'completed'
        ${dateFilter}
      `;
      const totalSales = await c.env.DB.prepare(totalSalesQuery).bind(...params).first();
      const productSalesQuery = `
        SELECT 
          p.name as product_name,
          COUNT(*) as orders,
          SUM(o.license_count) as licenses,
          SUM(o.total_price) as revenue
        FROM orders o
        JOIN products p ON o.product_id = p.id
        ${adminFilter}
        ${adminFilter ? "AND" : "WHERE"} o.status = 'completed'
        ${dateFilter}
        GROUP BY p.id, p.name
        ORDER BY revenue DESC
      `;
      const productSales = await c.env.DB.prepare(productSalesQuery).bind(...params).all();
      const trendQuery = `
        SELECT 
          DATE(o.created_at) as date,
          COUNT(*) as orders,
          SUM(o.license_count) as licenses,
          SUM(o.total_price) as revenue
        FROM orders o
        ${adminFilter}
        ${adminFilter ? "AND" : "WHERE"} o.status = 'completed'
        AND o.created_at >= date('now', '-30 days')
        GROUP BY DATE(o.created_at)
        ORDER BY date DESC
      `;
      const trendParams = admin.role === "super" ? [] : [admin.admin_id];
      const salesTrend = await c.env.DB.prepare(trendQuery).bind(...trendParams).all();
      const stats = {
        summary: {
          total_orders: totalSales?.total_orders || 0,
          total_licenses: totalSales?.total_licenses || 0,
          total_revenue: totalSales?.total_revenue || 0,
          period
        },
        by_product: productSales.results,
        trend: salesTrend.results
      };
      await c.env.CACHE.put(cacheKey, JSON.stringify(stats), { expirationTtl: 600 });
      return success(c, "\u9500\u552E\u7EDF\u8BA1\u83B7\u53D6\u6210\u529F", stats);
    } catch (error2) {
      console.error("Get sales stats error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
  app2.get("/api/stats/verification", authMiddleware(), async (c) => {
    try {
      const admin = c.get("admin");
      const query = c.req.query();
      const period = query.period || "month";
      const start_date = query.start_date;
      const end_date = query.end_date;
      let dateFilter = "";
      const params = [];
      if (start_date && end_date) {
        dateFilter = "AND vl.created_at BETWEEN ? AND ?";
        params.push(start_date, end_date);
      } else {
        const now = /* @__PURE__ */ new Date();
        let startDate;
        switch (period) {
          case "day":
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
          case "week":
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1e3);
            break;
          case "month":
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          case "year":
            startDate = new Date(now.getFullYear(), 0, 1);
            break;
          default:
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }
        dateFilter = "AND vl.created_at >= ?";
        params.push(startDate.toISOString());
      }
      let productFilter = "";
      if (admin.role !== "super") {
        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);
        if (adminInfo) {
          const { product_ids } = JSON.parse(adminInfo);
          if (product_ids && product_ids.length > 0) {
            productFilter = `AND l.product_id IN (${product_ids.map(() => "?").join(",")})`;
            params.push(...product_ids);
          } else {
            return success(c, "\u65E0\u9A8C\u8BC1\u6570\u636E\u53EF\u7528", {
              summary: { total_verifications: 0, successful_verifications: 0, failed_verifications: 0, success_rate: 0 },
              by_result: [],
              trend: []
            });
          }
        }
      }
      const totalQuery = `
        SELECT 
          COUNT(*) as total_verifications,
          SUM(CASE WHEN vl.result = 'success' THEN 1 ELSE 0 END) as successful_verifications,
          SUM(CASE WHEN vl.result = 'failed' THEN 1 ELSE 0 END) as failed_verifications
        FROM verification_logs vl
        JOIN licenses l ON vl.license_key = l.license_key
        WHERE 1=1
        ${productFilter}
        ${dateFilter}
      `;
      const totalStats = await c.env.DB.prepare(totalQuery).bind(...params).first();
      const resultQuery = `
        SELECT 
          vl.result,
          COUNT(*) as count
        FROM verification_logs vl
        JOIN licenses l ON vl.license_key = l.license_key
        WHERE 1=1
        ${productFilter}
        ${dateFilter}
        GROUP BY vl.result
      `;
      const resultStats = await c.env.DB.prepare(resultQuery).bind(...params).all();
      const trendQuery = `
        SELECT 
          DATE(vl.created_at) as date,
          COUNT(*) as total_verifications,
          SUM(CASE WHEN vl.result = 'success' THEN 1 ELSE 0 END) as successful_verifications
        FROM verification_logs vl
        JOIN licenses l ON vl.license_key = l.license_key
        WHERE vl.created_at >= date('now', '-30 days')
        ${productFilter ? productFilter.replace("AND", "AND") : ""}
        GROUP BY DATE(vl.created_at)
        ORDER BY date DESC
      `;
      const trendParams = admin.role === "super" ? [] : params.slice(1);
      const verificationTrend = await c.env.DB.prepare(trendQuery).bind(...trendParams).all();
      const successfulVerifications = totalStats?.successful_verifications || 0;
      const totalVerifications = totalStats?.total_verifications || 0;
      const successRate = totalVerifications > 0 ? successfulVerifications / totalVerifications * 100 : 0;
      const stats = {
        summary: {
          total_verifications: totalVerifications,
          successful_verifications: successfulVerifications,
          failed_verifications: totalStats?.failed_verifications || 0,
          success_rate: Math.round(successRate * 100) / 100,
          period
        },
        by_result: resultStats.results,
        trend: verificationTrend.results
      };
      return success(c, "\u9A8C\u8BC1\u7EDF\u8BA1\u83B7\u53D6\u6210\u529F", stats);
    } catch (error2) {
      console.error("Get verification stats error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
  app2.get("/api/stats/dashboard", authMiddleware(), async (c) => {
    try {
      const admin = c.get("admin");
      let productFilter = "";
      let adminFilter = "";
      const params = [];
      if (admin.role !== "super") {
        adminFilter = "WHERE admin_id = ?";
        params.push(admin.admin_id);
        const adminInfo = await c.env.CACHE.get(`admin:auth:${admin.admin_id}`);
        if (adminInfo) {
          const { product_ids } = JSON.parse(adminInfo);
          if (product_ids && product_ids.length > 0) {
            productFilter = `AND product_id IN (${product_ids.map(() => "?").join(",")})`;
            params.push(...product_ids);
          }
        }
      }
      const licenseQuery = `
        SELECT 
          COUNT(*) as total_licenses,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_licenses,
          SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_licenses,
          SUM(CASE WHEN status = 'revoked' THEN 1 ELSE 0 END) as revoked_licenses
        FROM licenses
        ${adminFilter}
        ${productFilter}
      `;
      const licenseStats = await c.env.DB.prepare(licenseQuery).bind(...params).first();
      const orderQuery = `
        SELECT 
          COUNT(*) as total_orders,
          SUM(total_price) as total_revenue,
          SUM(license_count) as total_licenses_sold
        FROM orders
        ${adminFilter}
        ${adminFilter ? "AND" : "WHERE"} status = 'completed'
        AND created_at >= date('now', 'start of month')
      `;
      const orderStats = await c.env.DB.prepare(orderQuery).bind(...params.slice(0, admin.role === "super" ? 0 : 1)).first();
      const verificationQuery = `
        SELECT 
          COUNT(*) as total_verifications,
          SUM(CASE WHEN result = 'success' THEN 1 ELSE 0 END) as successful_verifications
        FROM verification_logs vl
        ${admin.role === "super" ? "" : "JOIN licenses l ON vl.license_key = l.license_key"}
        WHERE vl.created_at >= date('now')
        ${admin.role === "super" ? "" : productFilter}
      `;
      const verificationParams = admin.role === "super" ? [] : productFilter ? params.slice(1) : [];
      const verificationStats = await c.env.DB.prepare(verificationQuery).bind(...verificationParams).first();
      const deviceQuery = `
        SELECT COUNT(DISTINCT device_id) as active_devices
        FROM devices d
        JOIN licenses l ON d.license_id = l.id
        WHERE d.last_verification >= date('now', '-7 days')
        ${admin.role === "super" ? "" : `AND l.product_id IN (${params.slice(1).map(() => "?").join(",")})`}
      `;
      const deviceParams = admin.role === "super" ? [] : params.slice(1);
      const deviceStats = await c.env.DB.prepare(deviceQuery).bind(...deviceParams).first();
      const dashboardData = {
        licenses: {
          total: licenseStats?.total_licenses || 0,
          active: licenseStats?.active_licenses || 0,
          expired: licenseStats?.expired_licenses || 0,
          revoked: licenseStats?.revoked_licenses || 0
        },
        orders_this_month: {
          total: orderStats?.total_orders || 0,
          revenue: orderStats?.total_revenue || 0,
          licenses_sold: orderStats?.total_licenses_sold || 0
        },
        verifications_today: {
          total: verificationStats?.total_verifications || 0,
          successful: verificationStats?.successful_verifications || 0
        },
        active_devices_week: deviceStats?.active_devices || 0
      };
      return success(c, "\u4EEA\u8868\u677F\u7EDF\u8BA1\u83B7\u53D6\u6210\u529F", dashboardData);
    } catch (error2) {
      console.error("Get dashboard stats error:", error2);
      return error(c, "Internal server error", 500);
    }
  });
}
__name(setupStatisticsRoutes, "setupStatisticsRoutes");

// src/routes/init.ts
init_modules_watch_stub();

// src/utils/init-db.ts
init_modules_watch_stub();
async function initAdminAccounts(DB) {
  console.log("\u6B63\u5728\u521D\u59CB\u5316\u7BA1\u7406\u5458\u8D26\u6237...");
  try {
    const existingSuperAdmin = await DB.prepare(
      "SELECT id FROM admins WHERE username = ?"
    ).bind("root").first();
    if (!existingSuperAdmin) {
      const superAdminPasswordHash = await hash("password", 10);
      await DB.prepare(`
        INSERT INTO admins (username, password_hash, role, status, product_ids, created_at, updated_at)
        VALUES (?, ?, 'super', 'active', NULL, datetime('now'), datetime('now'))
      `).bind("root", superAdminPasswordHash).run();
      console.log("\u2705 \u8D85\u7EA7\u7BA1\u7406\u5458\u8D26\u6237\u521B\u5EFA\u6210\u529F (\u7528\u6237\u540D: root, \u5BC6\u7801: password)");
    } else {
      console.log("\u2139\uFE0F \u8D85\u7EA7\u7BA1\u7406\u5458\u8D26\u6237\u5DF2\u5B58\u5728\uFF0C\u8DF3\u8FC7\u521B\u5EFA");
    }
    const existingNormalAdmin = await DB.prepare(
      "SELECT id FROM admins WHERE username = ?"
    ).bind("admin").first();
    if (!existingNormalAdmin) {
      const normalAdminPasswordHash = await hash("password", 10);
      const assignedProducts = JSON.stringify([1, 2]);
      await DB.prepare(`
        INSERT INTO admins (username, password_hash, role, status, product_ids, created_at, updated_at)
        VALUES (?, ?, 'normal', 'active', ?, datetime('now'), datetime('now'))
      `).bind("admin", normalAdminPasswordHash, assignedProducts).run();
      console.log("\u2705 \u666E\u901A\u7BA1\u7406\u5458\u8D26\u6237\u521B\u5EFA\u6210\u529F (\u7528\u6237\u540D: admin, \u5BC6\u7801: password, \u5206\u914D\u4EA7\u54C1: 1,2)");
    } else {
      console.log("\u2139\uFE0F \u666E\u901A\u7BA1\u7406\u5458\u8D26\u6237\u5DF2\u5B58\u5728\uFF0C\u8DF3\u8FC7\u521B\u5EFA");
    }
  } catch (error2) {
    console.error("\u274C \u521B\u5EFA\u7BA1\u7406\u5458\u8D26\u6237\u5931\u8D25:", error2);
    throw error2;
  }
}
__name(initAdminAccounts, "initAdminAccounts");
async function insertSampleLicenses(DB) {
  console.log("\u6B63\u5728\u63D2\u5165\u793A\u4F8B\u8BB8\u53EF\u8BC1\u6570\u636E...");
  try {
    const superAdmin = await DB.prepare("SELECT id FROM admins WHERE username = ?").bind("root").first();
    const normalAdmin = await DB.prepare("SELECT id FROM admins WHERE username = ?").bind("admin").first();
    if (!superAdmin || !normalAdmin) {
      throw new Error("\u7BA1\u7406\u5458\u8D26\u6237\u4E0D\u5B58\u5728\uFF0C\u8BF7\u5148\u8FD0\u884C\u7BA1\u7406\u5458\u521D\u59CB\u5316");
    }
    const sampleLicenses = [
      {
        product_id: 1,
        license_key: "BASIC-DEMO-001-ABCD-EFGH",
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1e3).toISOString(),
        // 1年后过期
        admin_id: superAdmin.id
      },
      {
        product_id: 2,
        license_key: "PRO-DEMO-002-IJKL-MNOP",
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1e3).toISOString(),
        admin_id: normalAdmin.id
      },
      {
        product_id: 3,
        license_key: "ENT-DEMO-003-QRST-UVWX",
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1e3).toISOString(),
        admin_id: superAdmin.id
      }
    ];
    for (const license of sampleLicenses) {
      const existing = await DB.prepare("SELECT id FROM licenses WHERE license_key = ?").bind(license.license_key).first();
      if (!existing) {
        await DB.prepare(`
          INSERT INTO licenses (product_id, license_key, status, expires_at, admin_id, created_at, updated_at)
          VALUES (?, ?, 'active', ?, ?, datetime('now'), datetime('now'))
        `).bind(
          license.product_id,
          license.license_key,
          license.expires_at,
          license.admin_id
        ).run();
        console.log(`\u2705 \u793A\u4F8B\u8BB8\u53EF\u8BC1\u521B\u5EFA\u6210\u529F: ${license.license_key}`);
      } else {
        console.log(`\u2139\uFE0F \u8BB8\u53EF\u8BC1\u5DF2\u5B58\u5728\uFF0C\u8DF3\u8FC7\u521B\u5EFA: ${license.license_key}`);
      }
    }
  } catch (error2) {
    console.error("\u274C \u63D2\u5165\u793A\u4F8B\u8BB8\u53EF\u8BC1\u5931\u8D25:", error2);
    throw error2;
  }
}
__name(insertSampleLicenses, "insertSampleLicenses");
async function insertSampleOrders(DB) {
  console.log("\u6B63\u5728\u63D2\u5165\u793A\u4F8B\u8BA2\u5355\u6570\u636E...");
  try {
    const normalAdmin = await DB.prepare("SELECT id FROM admins WHERE username = ?").bind("admin").first();
    if (!normalAdmin) {
      throw new Error("\u666E\u901A\u7BA1\u7406\u5458\u8D26\u6237\u4E0D\u5B58\u5728");
    }
    const sampleOrders = [
      {
        admin_id: normalAdmin.id,
        product_id: 1,
        license_count: 10,
        unit_price: 99,
        total_price: 990,
        status: "completed"
      },
      {
        admin_id: normalAdmin.id,
        product_id: 2,
        license_count: 5,
        unit_price: 299,
        total_price: 1495,
        status: "completed"
      }
    ];
    for (const order of sampleOrders) {
      await DB.prepare(`
        INSERT INTO orders (admin_id, product_id, license_count, unit_price, total_price, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `).bind(
        order.admin_id,
        order.product_id,
        order.license_count,
        order.unit_price,
        order.total_price,
        order.status
      ).run();
    }
    console.log("\u2705 \u793A\u4F8B\u8BA2\u5355\u6570\u636E\u63D2\u5165\u6210\u529F");
  } catch (error2) {
    console.error("\u274C \u63D2\u5165\u793A\u4F8B\u8BA2\u5355\u5931\u8D25:", error2);
    throw error2;
  }
}
__name(insertSampleOrders, "insertSampleOrders");
async function initializeDatabase(env) {
  console.log("\u{1F680} \u5F00\u59CB\u521D\u59CB\u5316\u6570\u636E\u5E93...");
  try {
    await initAdminAccounts(env.DB);
    await insertSampleLicenses(env.DB);
    await insertSampleOrders(env.DB);
    console.log("\u{1F389} \u6570\u636E\u5E93\u521D\u59CB\u5316\u5B8C\u6210\uFF01");
    console.log("");
    console.log("\u8D26\u6237\u4FE1\u606F\uFF1A");
    console.log("\u8D85\u7EA7\u7BA1\u7406\u5458 - \u7528\u6237\u540D: root, \u5BC6\u7801: password");
    console.log("\u666E\u901A\u7BA1\u7406\u5458 - \u7528\u6237\u540D: admin, \u5BC6\u7801: password");
    console.log("");
  } catch (error2) {
    console.error("\u{1F4A5} \u6570\u636E\u5E93\u521D\u59CB\u5316\u5931\u8D25:", error2);
    throw error2;
  }
}
__name(initializeDatabase, "initializeDatabase");

// src/routes/init.ts
function setupInitRoutes(app2) {
  app2.post("/init-db", async (c) => {
    try {
      const existingSuperAdmin = await c.env.DB.prepare(
        "SELECT id FROM admins WHERE role = ? LIMIT 1"
      ).bind("super").first();
      if (existingSuperAdmin) {
        return error(c, "\u6570\u636E\u5E93\u5DF2\u521D\u59CB\u5316\uFF0C\u65E0\u9700\u91CD\u590D\u64CD\u4F5C", 400);
      }
      await initializeDatabase(c.env);
      return success(c, "\u6570\u636E\u5E93\u521D\u59CB\u5316\u6210\u529F", {
        message: "\u7BA1\u7406\u5458\u8D26\u6237\u548C\u793A\u4F8B\u6570\u636E\u5DF2\u521B\u5EFA",
        accounts: [
          { username: "root", password: "password", role: "super" },
          { username: "admin", password: "password", role: "normal" }
        ]
      });
    } catch (error2) {
      console.error("Database initialization error:", error2);
      return error(c, "\u6570\u636E\u5E93\u521D\u59CB\u5316\u5931\u8D25", 500);
    }
  });
  app2.post("/reset-db", async (c) => {
    try {
      await c.env.DB.prepare("DELETE FROM verification_logs").run();
      await c.env.DB.prepare("DELETE FROM devices").run();
      await c.env.DB.prepare("DELETE FROM orders").run();
      await c.env.DB.prepare("DELETE FROM licenses").run();
      await c.env.DB.prepare("DELETE FROM admins").run();
      await c.env.DB.prepare("DELETE FROM products").run();
      await initializeDatabase(c.env);
      return success(c, "\u6570\u636E\u5E93\u91CD\u7F6E\u6210\u529F", {
        message: "\u6240\u6709\u6570\u636E\u5DF2\u6E05\u9664\u5E76\u91CD\u65B0\u521D\u59CB\u5316"
      });
    } catch (error2) {
      console.error("Database reset error:", error2);
      return error(c, "\u6570\u636E\u5E93\u91CD\u7F6E\u5931\u8D25", 500);
    }
  });
  app2.get("/db-status", async (c) => {
    try {
      const adminCount = await c.env.DB.prepare("SELECT COUNT(*) as count FROM admins").first();
      const productCount = await c.env.DB.prepare("SELECT COUNT(*) as count FROM products").first();
      const licenseCount = await c.env.DB.prepare("SELECT COUNT(*) as count FROM licenses").first();
      return success(c, "\u6570\u636E\u5E93\u72B6\u6001\u67E5\u8BE2\u6210\u529F", {
        admins: adminCount?.count || 0,
        products: productCount?.count || 0,
        licenses: licenseCount?.count || 0,
        initialized: (adminCount?.count || 0) > 0
      });
    } catch (error2) {
      console.error("Database status check error:", error2);
      return error(c, "\u6570\u636E\u5E93\u72B6\u6001\u67E5\u8BE2\u5931\u8D25", 500);
    }
  });
}
__name(setupInitRoutes, "setupInitRoutes");

// src/index.ts
var app = new Hono2();
app.use("*", corsMiddleware());
app.use("*", bindingsMiddleware());
app.get("/", (c) => {
  return c.json({
    success: true,
    message: "License Verification Service API",
    version: c.env.API_VERSION || "v1",
    timestamp: (/* @__PURE__ */ new Date()).toISOString()
  });
});
setupAuthRoutes(app);
setupUserRoutes(app);
setupLicenseRoutes(app);
setupClientRoutes(app);
setupOrderRoutes(app);
setupProductRoutes(app);
setupDistributorRoutes(app);
setupStatisticsRoutes(app);
setupInitRoutes(app);
var src_default = app;

// node_modules/.pnpm/wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
init_modules_watch_stub();
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/.pnpm/wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
init_modules_watch_stub();
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error2 = reduceError(e);
    return Response.json(error2, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-i513Wd/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/.pnpm/wrangler@4.26.1_@cloudflare+workers-types@4.20250731.0/node_modules/wrangler/templates/middleware/common.ts
init_modules_watch_stub();
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-i513Wd/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class ___Facade_ScheduledController__ {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  static {
    __name(this, "__Facade_ScheduledController__");
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof ___Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = /* @__PURE__ */ __name((request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    }, "#fetchDispatcher");
    #dispatcher = /* @__PURE__ */ __name((type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    }, "#dispatcher");
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
