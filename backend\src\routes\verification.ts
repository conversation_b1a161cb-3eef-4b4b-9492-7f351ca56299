import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { UniversalEncryption } from '../utils/encryption';
import * as response from '../utils/response';

export function setupVerificationRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  
  // 简化的验证接口 - 只返回配置数据供客户端本地比对
  app.post('/verify', async (c) => {
    try {
      const body = await c.req.json();
      const { license_key } = body;
      
      if (!license_key) {
        return response.error(c, '许可证密钥为必填项', 400);
      }
      
      // 查找许可证和相关信息
      const license = await c.env.DB.prepare(`
        SELECT l.*, pv.encryption_key, p.name as product_name, pv.version, pv.version_name
        FROM licenses l
        JOIN product_versions pv ON l.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        WHERE l.license_key = ?
      `).bind(license_key).first();

      if (!license) {
        return response.error(c, '许可证不存在', 404);
      }

      // 检查许可证状态
      if (license.status === 'revoked') {
        return response.error(c, '许可证已被撤销', 403);
      }
      
      if (license.status === 'inactive') {
        return response.error(c, '许可证未激活，请先激活许可证', 400);
      }
      
      // 检查是否有服务器配置（已激活）
      if (!license.server_config) {
        return response.error(c, '许可证配置不完整，请重新激活', 400);
      }
      
      try {
        // 解密服务器配置
        const serverConfig = UniversalEncryption.decryptJSON(
          license.server_config as string, 
          license.encryption_key as string
        );
        
        // 获取最新的客户端上报数据
        const latestReport = await c.env.DB.prepare(`
          SELECT client_data, reported_at FROM reports 
          WHERE license_key = ? 
          ORDER BY reported_at DESC 
          LIMIT 1
        `).bind(license_key).first();
        
        let clientData = null;
        if (latestReport?.client_data) {
          try {
            clientData = UniversalEncryption.decryptJSON(
              latestReport.client_data as string,
              license.encryption_key as string
            );
          } catch (error) {
            console.error('Decrypt client data error:', error);
            // 客户端数据解密失败不影响验证流程
          }
        }
        
        // 返回配置数据供客户端本地比对
        return response.success(c, '验证数据获取成功', {
          license_info: {
            product_name: license.product_name,
            version: license.version,
            version_name: license.version_name,
            status: license.status,
          },
          server_config: serverConfig,
          client_data: clientData,
          last_report_time: latestReport?.reported_at || null
        });
        
      } catch (decryptError) {
        console.error('Decrypt server config error:', decryptError);
        return response.error(c, '许可证配置解析失败', 500);
      }

    } catch (error) {
      console.error('License verification error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // 设备解绑接口 - 清除客户端上报数据
  app.post('/unbind', async (c) => {
    try {
      const body = await c.req.json();
      const { license_key } = body;

      if (!license_key) {
        return response.error(c, '许可证密钥为必填项', 400);
      }

      // 验证许可证存在
      const license = await c.env.DB.prepare(`
        SELECT id FROM licenses WHERE license_key = ? AND status = 'active'
      `).bind(license_key).first();

      if (!license) {
        return response.error(c, '许可证不存在或未激活', 404);
      }

      // 删除上报记录
      const result = await c.env.DB.prepare(`
        DELETE FROM reports WHERE license_key = ?
      `).bind(license_key).run();

      if (result.meta.changes === 0) {
        return response.error(c, '未找到需要清除的数据', 400);
      }

      return response.success(c, '设备解绑成功');

    } catch (error) {
      console.error('Device unbind error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
}