import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import * as response from '../utils/response';

export function setupVerificationRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // License verification endpoint
  app.post('/verify', async (c) => {
    try {
      const body = await c.req.json();
      const { license_key, device_id, product_features } = body;
      
      if (!license_key) {
        return response.error(c, '许可证密钥为必填项', 400);
      }
      
      // Check cache first
      const cacheKey = `license:verify:${license_key}`;
      const cached = await c.env.CACHE.get(cacheKey);
      
      if (cached) {
        const cachedData = JSON.parse(cached);
        return response.success(c, '许可证验证成功(缓存)', {
          license_info: cachedData,
        });
      }

      // Query database
      const license = await c.env.DB.prepare(`
        SELECT l.*, p.name as product_name, p.verification_strategy, p.max_devices as product_max_devices, p.features
        FROM licenses l
        JOIN products p ON l.product_id = p.id
        WHERE l.license_key = ? AND l.status = 'active' AND p.status = 'active'
      `).bind(license_key).first();

      if (!license) {
        // Log failed verification
        await c.env.DB.prepare(`
          INSERT INTO verification_logs (license_key, device_id, result, reason, ip_address, user_agent, created_at)
          VALUES (?, ?, 'failed', 'Invalid or inactive license', ?, ?, datetime('now'))
        `).bind(
          license_key,
          device_id || null,
          c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || '',
          c.req.header('User-Agent') || ''
        ).run();

        return response.error(c, '无效或已停用的许可证密钥', 400);
      }

      // Check expiration
      if (license.expires_at && new Date(license.expires_at as string) < new Date()) {
        return response.error(c, '许可证已过期', 400);
      }

      // Check device limits
      let currentDevices = 0;
      if (license.verification_strategy === 'device_count' && device_id) {
        const deviceCount = await c.env.DB.prepare(`
          SELECT COUNT(*) as count FROM devices WHERE license_id = ?
        `).bind(license.id).first();
        
        currentDevices = (deviceCount?.count as number) || 0;
        const maxDevices = (license.max_devices as number) || (license.product_max_devices as number) || 1;

        // Check if device already exists
        const existingDevice = await c.env.DB.prepare(`
          SELECT id FROM devices WHERE license_id = ? AND device_id = ?
        `).bind(license.id, device_id).first();

        if (!existingDevice && currentDevices >= maxDevices) {
          return response.error(c, `设备数量超限，最多允许${maxDevices}台设备`, 400);
        }

        // Add or update device
        if (!existingDevice) {
          await c.env.DB.prepare(`
            INSERT INTO devices (license_id, device_id, device_info, last_verification, created_at, updated_at)
            VALUES (?, ?, ?, datetime('now'), datetime('now'), datetime('now'))
          `).bind(license.id as number, device_id, JSON.stringify({ user_agent: c.req.header('User-Agent') })).run();
          currentDevices++;
        } else {
          await c.env.DB.prepare(`
            UPDATE devices SET last_verification = datetime('now'), updated_at = datetime('now')
            WHERE license_id = ? AND device_id = ?
          `).bind(license.id as number, device_id).run();
        }
      }

      // Parse features
      let features: string[] = [];
      if (license.features) {
        try {
          features = JSON.parse(license.features as string);
        } catch {
          features = (license.features as string).split(',').map((f: string) => f.trim());
        }
      }

      const licenseInfo = {
        product_name: license.product_name,
        expires_at: license.expires_at,
        max_devices: license.max_devices || license.product_max_devices,
        current_devices: currentDevices,
        features,
      };

      // Cache the result for 5 minutes
      await c.env.CACHE.put(cacheKey, JSON.stringify(licenseInfo), { expirationTtl: 300 });

      // Log successful verification
      await c.env.DB.prepare(`
        INSERT INTO verification_logs (license_key, device_id, result, reason, ip_address, user_agent, created_at)
        VALUES (?, ?, 'success', 'License verified successfully', ?, ?, datetime('now'))
      `).bind(
        license_key,
        device_id || null,
        c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || '',
        c.req.header('User-Agent') || ''
      ).run();

      return response.success(c, '许可证验证成功', {
        license_info: licenseInfo,
      });

    } catch (error) {
      console.error('License verification error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Device unbinding endpoint
  app.post('/device/unbind', async (c) => {
    try {
      const body = await c.req.json();
      const { license_key, device_id } = body;

      if (!license_key || !device_id) {
        return response.error(c, '许可证密钥和设备ID为必填项', 400);
      }

      // Get license info
      const license = await c.env.DB.prepare(`
        SELECT id FROM licenses WHERE license_key = ? AND status = 'active'
      `).bind(license_key).first();

      if (!license) {
        return response.error(c, '无效或已停用的许可证密钥', 400);
      }

      // Remove device binding
      const result = await c.env.DB.prepare(`
        DELETE FROM devices WHERE license_id = ? AND device_id = ?
      `).bind(license.id as number, device_id).run();

      if (result.meta.changes === 0) {
        return response.error(c, '未找到设备绑定', 400);
      }

      // Clear cache
      const cacheKey = `license:verify:${license_key}`;
      await c.env.CACHE.delete(cacheKey);

      return response.success(c, '设备解绑成功');

    } catch (error) {
      console.error('Device unbind error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
}