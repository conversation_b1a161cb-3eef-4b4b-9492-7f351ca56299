import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware, adminOnly } from '../middleware/auth';
import * as response from '../utils/response';

export function setupVersionRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // ===========================
  // 产品版本管理接口 (Product Versions)
  // ===========================
  
  // Get versions for a product
  app.get('/api/products/:id/versions', authMiddleware(), async (c) => {
    try {
      const productId = parseInt(c.req.param('id'));
      
      if (!productId || productId <= 0) {
        return response.error(c, '无效的产品ID', 400);
      }
      
      // Check if product exists
      const product = await c.env.DB.prepare('SELECT id, name FROM products WHERE id = ?').bind(productId).first();
      if (!product) {
        return response.error(c, '产品不存在', 404);
      }
      
      const versions = await c.env.DB.prepare(`
        SELECT pv.*, COUNT(l.id) as license_count
        FROM product_versions pv
        LEFT JOIN licenses l ON pv.id = l.version_id
        WHERE pv.product_id = ?
        GROUP BY pv.id
        ORDER BY pv.version DESC
      `).bind(productId).all();
      
      return response.success(c, '产品版本列表获取成功', {
        product,
        versions: versions.results,
      });
    } catch (error) {
      console.error('Get product versions error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Create product version (admin only)
  app.post('/api/products/:id/versions', authMiddleware(), adminOnly(), async (c) => {
    try {
      const productId = parseInt(c.req.param('id'));
      const body = await c.req.json();
      const { 
        version, 
        version_name, 
        description, 
        features, 
        verification_strategy, 
        max_devices, 
        default_price, 
        download_link, 
        changelog 
      } = body;
      
      if (!productId || productId <= 0) {
        return response.error(c, '无效的产品ID', 400);
      }
      
      if (!version || !verification_strategy || !default_price) {
        return response.error(c, '版本号、验证策略和默认价格不能为空', 400);
      }
      
      // Check if product exists
      const product = await c.env.DB.prepare('SELECT id FROM products WHERE id = ? AND status = \'active\'').bind(productId).first();
      if (!product) {
        return response.error(c, '产品不存在或已停用', 400);
      }
      
      // Check if version already exists
      const existing = await c.env.DB.prepare('SELECT id FROM product_versions WHERE product_id = ? AND version = ?').bind(productId, version).first();
      if (existing) {
        return response.error(c, '该版本号已存在', 400);
      }
      
      const featuresJson = features ? JSON.stringify(features) : null;
      
      const result = await c.env.DB.prepare(`
        INSERT INTO product_versions 
        (product_id, version, version_name, description, features, verification_strategy, max_devices, default_price, download_link, changelog, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', datetime('now'), datetime('now'))
      `).bind(
        productId, 
        version, 
        version_name || null, 
        description || null, 
        featuresJson, 
        verification_strategy, 
        max_devices || null, 
        default_price, 
        download_link || null, 
        changelog || null
      ).run();
      
      return response.success(c, '产品版本创建成功', { id: result.meta.last_row_id });
    } catch (error) {
      console.error('Create product version error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Get product version by ID
  app.get('/api/versions/:id', authMiddleware(), async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的版本ID', 400);
      }
      
      const version = await c.env.DB.prepare(`
        SELECT pv.*, p.name as product_name
        FROM product_versions pv
        JOIN products p ON pv.product_id = p.id
        WHERE pv.id = ?
      `).bind(id).first();
      
      if (!version) {
        return response.error(c, '产品版本不存在', 404);
      }
      
      return response.success(c, '产品版本信息获取成功', { version });
    } catch (error) {
      console.error('Get product version error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Update product version (admin only)
  app.put('/api/versions/:id', authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      const updateData = await c.req.json();
      
      if (!id || id <= 0) {
        return response.error(c, '无效的版本ID', 400);
      }
      
      // Check if version exists
      const existing = await c.env.DB.prepare('SELECT id FROM product_versions WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '产品版本不存在', 404);
      }
      
      // Build update query
      const updateFields: string[] = [];
      const params: any[] = [];
      
      if (updateData.version !== undefined) {
        updateFields.push('version = ?');
        params.push(updateData.version);
      }
      if (updateData.version_name !== undefined) {
        updateFields.push('version_name = ?');
        params.push(updateData.version_name);
      }
      if (updateData.description !== undefined) {
        updateFields.push('description = ?');
        params.push(updateData.description);
      }
      if (updateData.features !== undefined) {
        updateFields.push('features = ?');
        params.push(updateData.features ? JSON.stringify(updateData.features) : null);
      }
      if (updateData.verification_strategy !== undefined) {
        updateFields.push('verification_strategy = ?');
        params.push(updateData.verification_strategy);
      }
      if (updateData.max_devices !== undefined) {
        updateFields.push('max_devices = ?');
        params.push(updateData.max_devices);
      }
      if (updateData.default_price !== undefined) {
        updateFields.push('default_price = ?');
        params.push(updateData.default_price);
      }
      if (updateData.download_link !== undefined) {
        updateFields.push('download_link = ?');
        params.push(updateData.download_link);
      }
      if (updateData.changelog !== undefined) {
        updateFields.push('changelog = ?');
        params.push(updateData.changelog);
      }
      if (updateData.status !== undefined) {
        updateFields.push('status = ?');
        params.push(updateData.status);
      }
      
      if (updateFields.length === 0) {
        return response.error(c, '没有要更新的字段', 400);
      }
      
      updateFields.push('updated_at = datetime(\'now\')');
      params.push(id);
      
      const query = `UPDATE product_versions SET ${updateFields.join(', ')} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      
      return response.success(c, '产品版本更新成功');
    } catch (error) {
      console.error('Update product version error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Delete product version (admin only)
  app.delete('/api/versions/:id', authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的版本ID', 400);
      }
      
      // Check if version exists
      const existing = await c.env.DB.prepare('SELECT id FROM product_versions WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '产品版本不存在', 404);
      }
      
      // Check if version has active licenses
      const activeLicenses = await c.env.DB.prepare('SELECT COUNT(*) as count FROM licenses WHERE version_id = ? AND status = \'active\'').bind(id).first();
      if (activeLicenses && (activeLicenses.count as number) > 0) {
        return response.error(c, '不能删除拥有活跃许可证的产品版本', 409);
      }
      
      // Delete related data
      await c.env.DB.prepare('DELETE FROM distributor_authorizations WHERE version_id = ?').bind(id).run();
      await c.env.DB.prepare('DELETE FROM product_versions WHERE id = ?').bind(id).run();
      
      return response.success(c, '产品版本删除成功');
    } catch (error) {
      console.error('Delete product version error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
}