import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { JWTService } from '../utils/auth';
import * as response from '../utils/response';
import * as bcrypt from 'bcryptjs';

export function setupAuthRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // User login endpoint
  app.post('/api/auth/login', async (c) => {
    try {
      const body = await c.req.json();
      const { username, password } = body;

      if (!username || !password) {
        return response.error(c, '用户名和密码不能为空', 400);
      }

      // Get user by username
      const user = await c.env.DB.prepare(`
        SELECT id, username, password_hash, role, status, display_name, contact_wechat, avatar_url
        FROM users
        WHERE username = ? AND status = 'active'
      `).bind(username).first();

      if (!user) {
        return response.error(c, '用户名或密码错误', 401);
      }

      // Verify password using bcrypt
      const isPasswordValid = await bcrypt.compare(password, user.password_hash as string);

      if (!isPasswordValid) {
        return response.error(c, '用户名或密码错误', 401);
      }

      const jwtService = new JWTService(c.env.JWT_SECRET);

      // Generate tokens
      const tokenPayload = {
        admin_id: user.id as number,
        username: user.username as string,
        role: user.role as string,
      };

      const token = await jwtService.generateToken(tokenPayload);

      // Get authorized products for distributors
      let authorizedProducts: string[] = [];
      if (user.role === 'distributor') {
        const authorizations = await c.env.DB.prepare(`
          SELECT version_id FROM distributor_authorizations 
          WHERE distributor_id = ? AND status = 'active'
        `).bind(user.id).all();
        
        authorizedProducts = authorizations.results.map((auth: any) => auth.version_id.toString());
      }

      // Cache user info
      const cacheKey = `user:auth:${user.id}`;
      await c.env.CACHE.put(cacheKey, JSON.stringify({
        id: user.id,
        username: user.username,
        role: user.role,
        display_name: user.display_name,
        contact_wechat: user.contact_wechat,
        avatar_url: user.avatar_url,
        authorized_products: authorizedProducts,
      }), { expirationTtl: 3600 }); // 1 hour

      return response.success(c, '登录成功', {
        token,
        admin_id: user.id as number,
        username: user.username as string,
        role: user.role as string,
        authorized_products: authorizedProducts,
        expires_in: 86400,
      });

    } catch (error) {
      console.error('User login error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Token refresh endpoint
  app.post('/api/auth/refresh', async (c) => {
    try {
      const authHeader = c.req.header('Authorization');
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.error(c, '缺少有效的刷新token', 401);
      }

      const refresh_token = authHeader.substring(7);

      const jwtService = new JWTService(c.env.JWT_SECRET);

      // Verify refresh token
      let payload;
      try {
        payload = await jwtService.verifyToken(refresh_token);
      } catch {
        return response.error(c, '无效或过期的刷新token', 401);
      }

      // Get fresh user info
      const user = await c.env.DB.prepare(`
        SELECT id, username, role, status, display_name, contact_wechat, avatar_url
        FROM users
        WHERE id = ? AND status = 'active'
      `).bind(payload.admin_id).first();

      if (!user) {
        return response.error(c, '用户账号不存在或已禁用', 401);
      }

      // Generate new token
      const tokenPayload = {
        admin_id: user.id as number,
        username: user.username as string,
        role: user.role as string,
      };

      const token = await jwtService.generateToken(tokenPayload);

      // Get authorized products for distributors
      let authorizedProducts: string[] = [];
      if (user.role === 'distributor') {
        const authorizations = await c.env.DB.prepare(`
          SELECT version_id FROM distributor_authorizations 
          WHERE distributor_id = ? AND status = 'active'
        `).bind(user.id).all();
        
        authorizedProducts = authorizations.results.map((auth: any) => auth.version_id.toString());
      }

      // Update cache
      const cacheKey = `user:auth:${user.id}`;
      await c.env.CACHE.put(cacheKey, JSON.stringify({
        id: user.id,
        username: user.username,
        role: user.role,
        display_name: user.display_name,
        contact_wechat: user.contact_wechat,
        avatar_url: user.avatar_url,
        authorized_products: authorizedProducts,
      }), { expirationTtl: 3600 });

      return response.success(c, 'Token刷新成功', {
        token,
        expires_in: 86400,
      });

    } catch (error) {
      console.error('Token refresh error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Logout endpoint
  app.post('/api/auth/logout', async (c) => {
    try {
      const authHeader = c.req.header('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.error(c, '需要授权头', 401);
      }

      const token = authHeader.substring(7);
      const jwtService = new JWTService(c.env.JWT_SECRET);

      try {
        const payload = await jwtService.verifyToken(token);
        
        // Clear cache
        const cacheKey = `user:auth:${payload.admin_id}`;
        await c.env.CACHE.delete(cacheKey);
        
      } catch {
        // Token is invalid, but we still return success for logout
      }

      return response.success(c, '登出成功');

    } catch (error) {
      console.error('Logout error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
}