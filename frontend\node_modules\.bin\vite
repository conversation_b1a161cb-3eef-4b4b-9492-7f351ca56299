#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/code/verify/frontend/node_modules/.pnpm/vite@7.0.6_@types+node@24.1.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/bin/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/frontend/node_modules/.pnpm/vite@7.0.6_@types+node@24.1.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/frontend/node_modules/.pnpm/vite@7.0.6_@types+node@24.1.0_jiti@2.5.1_lightningcss@1.30.1/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/Users/<USER>/code/verify/frontend/node_modules/.pnpm/vite@7.0.6_@types+node@24.1.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/bin/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/frontend/node_modules/.pnpm/vite@7.0.6_@types+node@24.1.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/frontend/node_modules/.pnpm/vite@7.0.6_@types+node@24.1.0_jiti@2.5.1_lightningcss@1.30.1/node_modules:/proc/cygdrive/c/Users/<USER>/code/verify/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/vite@7.0.6_@types+node@24.1.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../.pnpm/vite@7.0.6_@types+node@24.1.0_jiti@2.5.1_lightningcss@1.30.1/node_modules/vite/bin/vite.js" "$@"
fi
