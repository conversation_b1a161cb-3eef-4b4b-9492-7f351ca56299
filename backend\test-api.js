#!/usr/bin/env node

/**
 * 许可证验证系统 API 测试脚本
 * 测试所有接口功能
 */

const BASE_URL = 'http://localhost:8787';

let adminToken = '';
let distributorToken = '';

// 测试结果统计
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
};

// 工具函数
function logTest(testName, success, message = '') {
  testResults.total++;
  if (success) {
    testResults.passed++;
    console.log(`✅ ${testName} - PASSED ${message}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName} - FAILED ${message}`);
  }
}

async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });
  
  const data = await response.json();
  return { response, data };
}

// 详细响应日志函数
function logResponse(testName, response, data, showFullResponse = false) {
  console.log(`\n📊 ${testName} - 响应详情:`);
  console.log(`   状态码: ${response.status}`);
  console.log(`   成功状态: ${response.ok}`);
  
  if (showFullResponse) {
    console.log(`   完整响应:`, JSON.stringify(data, null, 2));
  } else {
    // 显示关键信息
    if (data.success !== undefined) {
      console.log(`   API成功状态: ${data.success}`);
    }
    if (data.message) {
      console.log(`   消息: ${data.message}`);
    }
    if (data.data) {
      console.log(`   数据摘要: ${JSON.stringify(data.data, null, 2).substring(0, 200)}...`);
    }
    if (data.error) {
      console.log(`   错误: ${data.error}`);
    }
  }
}

// 1. 测试健康检查
async function testHealthCheck() {
  console.log('\n🏥 测试健康检查');
  try {
    const { response, data } = await makeRequest('/');
    logTest('健康检查', response.ok, `版本: ${data.version}`);
  } catch (error) {
    logTest('健康检查', false, error.message);
  }
}

// 2. 测试数据库状态
async function testDatabaseStatus() {
  console.log('\n📊 测试数据库状态');
  try {
    const { response, data } = await makeRequest('/db-status');
    logTest('数据库状态查询', response.ok, 
      `用户: ${data.data?.users}, 产品: ${data.data?.products}, 版本: ${data.data?.versions}`);
  } catch (error) {
    logTest('数据库状态查询', false, error.message);
  }
}

// 3. 测试用户认证
async function testAuthentication() {
  console.log('\n🔐 测试用户认证');
  
  // 管理员登录
  try {
    const { response, data } = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        username: 'admin',
        password: 'password'
      })
    });
    
    logResponse('管理员登录', response, data);
    
    if (response.ok && data.data?.token) {
      adminToken = data.data.token;
      logTest('管理员登录', true, `Token: ${adminToken.substring(0, 20)}...`);
    } else {
      logTest('管理员登录', false, data.message);
    }
  } catch (error) {
    logTest('管理员登录', false, error.message);
  }

  // 分发商登录
  try {
    const { response, data } = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        username: 'xiaoming',
        password: 'password'
      })
    });
    
    logResponse('分发商登录', response, data);
    
    if (response.ok && data.data?.token) {
      distributorToken = data.data.token;
      logTest('分发商登录', true, `Token: ${distributorToken.substring(0, 20)}...`);
    } else {
      logTest('分发商登录', false, data.message);
    }
  } catch (error) {
    logTest('分发商登录', false, error.message);
  }

  // 测试错误登录
  try {
    const { response, data } = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        username: 'admin',
        password: 'wrongpassword'
      })
    });
    
    logResponse('错误登录尝试', response, data);
    logTest('错误登录拒绝', !response.ok, `状态码: ${response.status}`);
  } catch (error) {
    logTest('错误登录拒绝', false, error.message);
  }
}

// 4. 测试产品管理
async function testProductManagement() {
  console.log('\n📦 测试产品管理');

  // 获取产品列表
  try {
    const { response, data } = await makeRequest('/api/products', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logTest('获取产品列表', response.ok, 
      `产品数量: ${data.data?.products?.length || 0}`);
  } catch (error) {
    logTest('获取产品列表', false, error.message);
  }

  // 获取单个产品详情
  try {
    const { response, data } = await makeRequest('/api/products/1', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logTest('获取产品详情', response.ok, 
      `产品名称: ${data.data?.product?.name}`);
  } catch (error) {
    logTest('获取产品详情', false, error.message);
  }
}

// 5. 测试版本管理
async function testVersionManagement() {
  console.log('\n🏷️ 测试版本管理');

  // 获取产品版本列表
  try {
    const { response, data } = await makeRequest('/api/products/1/versions', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logTest('获取版本列表', response.ok, 
      `版本数量: ${data.data?.versions?.length || 0}`);
  } catch (error) {
    logTest('获取版本列表', false, error.message);
  }

  // 获取单个版本详情
  try {
    const { response, data } = await makeRequest('/api/versions/1', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logTest('获取版本详情', response.ok, 
      `版本号: ${data.data?.version?.version}, 价格: ¥${data.data?.version?.default_price}`);
  } catch (error) {
    logTest('获取版本详情', false, error.message);
  }
}

// 6. 测试许可证管理
async function testLicenseManagement() {
  console.log('\n🔑 测试许可证管理');

  // 获取许可证列表（管理员视角）
  try {
    const { response, data } = await makeRequest('/api/licenses', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logTest('管理员获取许可证列表', response.ok, 
      `许可证数量: ${data.data?.licenses?.length || 0}`);
  } catch (error) {
    logTest('管理员获取许可证列表', false, error.message);
  }

  // 获取许可证列表（分发商视角）
  try {
    const { response, data } = await makeRequest('/api/licenses', {
      headers: { Authorization: `Bearer ${distributorToken}` }
    });
    
    logTest('分发商获取许可证列表', response.ok, 
      `许可证数量: ${data.data?.licenses?.length || 0}`);
  } catch (error) {
    logTest('分发商获取许可证列表', false, error.message);
  }

  // 生成新许可证
  try {
    const { response, data } = await makeRequest('/api/licenses', {
      method: 'POST',
      headers: { Authorization: `Bearer ${distributorToken}` },
      body: JSON.stringify({
        version_id: 1,
        count: 1,
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
      })
    });
    
    logTest('生成新许可证', response.ok, 
      `生成数量: ${data.data?.generated_count || 0}`);
  } catch (error) {
    logTest('生成新许可证', false, error.message);
  }
}

// 7. 测试授权管理
async function testAuthorizationManagement() {
  console.log('\n🤝 测试授权管理');

  // 获取授权列表
  try {
    const { response, data } = await makeRequest('/api/authorizations', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logTest('获取授权列表', response.ok, 
      `授权数量: ${data.data?.authorizations?.length || 0}`);
  } catch (error) {
    logTest('获取授权列表', false, error.message);
  }

  // 分发商查看自己的授权
  try {
    const { response, data } = await makeRequest('/api/distributors/2/authorizations', {
      headers: { Authorization: `Bearer ${distributorToken}` }
    });
    
    logTest('分发商查看授权', response.ok, 
      `授权版本数量: ${data.data?.authorizations?.length || 0}`);
  } catch (error) {
    logTest('分发商查看授权', false, error.message);
  }
}

// 8. 测试店铺页面
async function testStorePages() {
  console.log('\n🏪 测试店铺页面');

  // 访问分发商店铺
  try {
    const { response, data } = await makeRequest('/api/store/xiaoming');
    
    logTest('访问分发商店铺', response.ok, 
      `店铺名称: ${data.data?.store?.distributor?.display_name}, 商品数量: ${data.data?.store?.products?.length || 0}`);
  } catch (error) {
    logTest('访问分发商店铺', false, error.message);
  }

  // 访问不存在的店铺
  try {
    const { response, data } = await makeRequest('/api/store/nonexistent');
    
    logTest('访问不存在店铺被拒绝', !response.ok, `状态码: ${response.status}`);
  } catch (error) {
    logTest('访问不存在店铺被拒绝', false, error.message);
  }
}

// 9. 测试许可证验证
async function testLicenseVerification() {
  console.log('\n✅ 测试许可证验证');

  // 验证有效许可证
  try {
    const { response, data } = await makeRequest('/verify', {
      method: 'POST',
      body: JSON.stringify({
        license_key: 'DEMO-BASIC-001-ABCD-EFGH',
        device_id: 'TEST-DEVICE-001'
      })
    });
    
    logResponse('验证有效许可证', response, data);
    logTest('验证有效许可证', response.ok, 
      `产品: ${data.data?.license_info?.product_name}, 设备数: ${data.data?.license_info?.current_devices}`);
  } catch (error) {
    logTest('验证有效许可证', false, error.message);
  }

  // 验证无效许可证
  try {
    const { response, data } = await makeRequest('/verify', {
      method: 'POST',
      body: JSON.stringify({
        license_key: 'INVALID-LICENSE-KEY',
        device_id: 'TEST-DEVICE-001'
      })
    });
    
    logResponse('验证无效许可证', response, data);
    logTest('验证无效许可证被拒绝', !response.ok, `状态码: ${response.status}`);
  } catch (error) {
    logTest('验证无效许可证被拒绝', false, error.message);
  }

  // 重复验证相同设备（应该成功）
  try {
    const { response, data } = await makeRequest('/verify', {
      method: 'POST',
      body: JSON.stringify({
        license_key: 'DEMO-BASIC-001-ABCD-EFGH',
        device_id: 'TEST-DEVICE-001'
      })
    });
    
    logResponse('重复验证相同设备', response, data);
    logTest('重复验证相同设备', response.ok, 
      `设备数: ${data.data?.license_info?.current_devices}`);
  } catch (error) {
    logTest('重复验证相同设备', false, error.message);
  }

  // 测试设备数量超限验证失败
  console.log('\n🚫 测试设备数量超限场景');
  
  // 基础版许可证max_devices=1，专业版max_devices=3
  // 先用专业版许可证测试多设备绑定
  const deviceIds = ['DEVICE-PRO-001', 'DEVICE-PRO-002', 'DEVICE-PRO-003', 'DEVICE-PRO-004', 'DEVICE-PRO-005'];
  
  for (let i = 0; i < deviceIds.length; i++) {
    try {
      const { response, data } = await makeRequest('/verify', {
        method: 'POST',
        body: JSON.stringify({
          license_key: 'DEMO-PRO-002-IJKL-MNOP', // 专业版许可证，最多3台设备
          device_id: deviceIds[i]
        })
      });
      
      if (i < 3) { // 前3台设备应该成功
        logResponse(`专业版绑定设备${i+1}`, response, data);
        logTest(`专业版绑定设备${i+1}`, response.ok, `设备ID: ${deviceIds[i]}`);
      } else {
        // 第4、5台设备应该失败（设备数量超限）
        logResponse(`设备数量超限测试${i+1}`, response, data);
        logTest(`设备数量超限被拒绝${i+1}`, !response.ok, `设备ID: ${deviceIds[i]}, 状态码: ${response.status}`);
      }
    } catch (error) {
      logTest(`设备绑定测试${i+1}`, false, error.message);
    }
  }
  
  // 再测试基础版许可证的设备超限（已经绑定了TEST-DEVICE-001）
  try {
    const { response, data } = await makeRequest('/verify', {
      method: 'POST',
      body: JSON.stringify({
        license_key: 'DEMO-BASIC-001-ABCD-EFGH', // 基础版许可证，最多1台设备
        device_id: 'ANOTHER-BASIC-DEVICE'
      })
    });
    
    logResponse('基础版设备超限测试', response, data);
    logTest('基础版设备超限被拒绝', !response.ok, `设备ID: ANOTHER-BASIC-DEVICE, 状态码: ${response.status}`);
  } catch (error) {
    logTest('基础版设备超限测试', false, error.message);
  }

  // 测试功能未开启验证失败场景
  console.log('\n🔒 测试功能未开启场景');
  
  // 这里需要一个有功能限制的许可证来测试
  // 先验证专业版许可证以确保其功能正常
  try {
    const { response, data } = await makeRequest('/verify', {
      method: 'POST',
      body: JSON.stringify({
        license_key: 'DEMO-PRO-002-IJKL-MNOP',
        device_id: 'PRO-DEVICE-001'
      })
    });
    
    logResponse('专业版许可证验证', response, data);
    
    if (response.ok) {
      const features = data.data?.license_info?.features || [];
      logTest('专业版许可证验证', true, `功能列表: ${features.join(', ')}`);
      
      // 检查功能权限
      const hasAdvancedFeature = features.includes('advanced_analytics');
      const hasExportFeature = features.includes('data_export');
      
      logTest('高级分析功能检查', hasAdvancedFeature, `功能状态: ${hasAdvancedFeature ? '已开启' : '未授权'}`);
      logTest('数据导出功能检查', hasExportFeature, `功能状态: ${hasExportFeature ? '已开启' : '未授权'}`);
    } else {
      logTest('专业版许可证验证', false, `状态码: ${response.status}`);
    }
  } catch (error) {
    logTest('专业版许可证验证', false, error.message);
  }
}

// 10. 测试设备解绑
async function testDeviceUnbinding() {
  console.log('\n🔓 测试设备解绑');

  try {  
    const { response, data } = await makeRequest('/unbind', {
      method: 'POST',
      body: JSON.stringify({
        license_key: 'DEMO-BASIC-001-ABCD-EFGH',
        device_id: 'TEST-DEVICE-001'
      })
    });
    
    logTest('设备解绑', response.ok, data.message);
  } catch (error) {
    logTest('设备解绑', false, error.message);
  }
}

// 11. 测试订单管理
async function testOrderManagement() {
  console.log('\n💰 测试订单管理');

  // 获取订单列表
  try {
    const { response, data } = await makeRequest('/api/orders', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logTest('获取订单列表', response.ok, 
      `订单数量: ${data.data?.orders?.length || 0}`);
  } catch (error) {
    logTest('获取订单列表', false, error.message);
  }
}

// 12. 测试用户管理
async function testUserManagement() {
  console.log('\n👥 测试用户管理');

  // 获取用户列表
  try {
    const { response, data } = await makeRequest('/api/users', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logTest('获取用户列表', response.ok, 
      `用户数量: ${data.data?.users?.length || 0}`);
  } catch (error) {
    logTest('获取用户列表', false, error.message);
  }

  // 分发商尝试访问用户管理（应该被拒绝）
  try {
    const { response, data } = await makeRequest('/api/users', {
      headers: { Authorization: `Bearer ${distributorToken}` }
    });
    
    logTest('分发商访问用户管理被拒绝', !response.ok, `状态码: ${response.status}`);
  } catch (error) {
    logTest('分发商访问用户管理被拒绝', false, error.message);
  }
}

// 13. 测试统计接口
async function testStatisticsInterfaces() {
  console.log('\n📊 测试统计接口');

  // 测试销售统计
  try {
    const { response, data } = await makeRequest('/api/stats/sales?period=month', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logResponse('销售统计查询', response, data);
    logTest('销售统计查询', response.ok, 
      `订单数: ${data.data?.summary?.total_orders}, 收入: ¥${data.data?.summary?.total_revenue}`);
  } catch (error) {
    logTest('销售统计查询', false, error.message);
  }

  // 测试分发商销售统计
  try {
    const { response, data } = await makeRequest('/api/stats/sales?period=week', {
      headers: { Authorization: `Bearer ${distributorToken}` }
    });
    
    logResponse('分发商销售统计查询', response, data);
    logTest('分发商销售统计查询', response.ok, 
      `订单数: ${data.data?.summary?.total_orders}, 收入: ¥${data.data?.summary?.total_revenue}`);
  } catch (error) {
    logTest('分发商销售统计查询', false, error.message);
  }

  // 测试验证统计
  try {
    const { response, data } = await makeRequest('/api/stats/verification?period=day', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logResponse('验证统计查询', response, data);
    logTest('验证统计查询', response.ok, 
      `验证次数: ${data.data?.summary?.total_verifications}, 成功率: ${data.data?.summary?.success_rate}%`);
  } catch (error) {
    logTest('验证统计查询', false, error.message);
  }

  // 测试仪表板统计
  try {
    const { response, data } = await makeRequest('/api/stats/dashboard', {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logResponse('仪表板统计查询', response, data, true); // 显示完整响应
    logTest('仪表板统计查询', response.ok, 
      `活跃许可证: ${data.data?.licenses?.active}, 本月订单: ${data.data?.orders_this_month?.total}`);
  } catch (error) {
    logTest('仪表板统计查询', false, error.message);
  }

  // 测试分发商仪表板统计
  try {
    const { response, data } = await makeRequest('/api/stats/dashboard', {
      headers: { Authorization: `Bearer ${distributorToken}` }
    });
    
    logResponse('分发商仪表板统计查询', response, data);
    logTest('分发商仪表板统计查询', response.ok, 
      `活跃许可证: ${data.data?.licenses?.active}, 本月订单: ${data.data?.orders_this_month?.total}`);
  } catch (error) {
    logTest('分发商仪表板统计查询', false, error.message);
  }

  // 测试自定义日期范围统计
  const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  const endDate = new Date().toISOString().split('T')[0];
  
  try {
    const { response, data } = await makeRequest(`/api/stats/sales?start_date=${startDate}&end_date=${endDate}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    logResponse('自定义日期范围统计', response, data);
    logTest('自定义日期范围统计', response.ok, 
      `日期范围: ${startDate} 到 ${endDate}, 订单数: ${data.data?.summary?.total_orders}`);
  } catch (error) {
    logTest('自定义日期范围统计', false, error.message);
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始运行所有API测试...\n');
  console.log(`测试目标: ${BASE_URL}`);
  
  await testHealthCheck();
  await testDatabaseStatus();
  await testAuthentication();
  await testProductManagement();
  await testVersionManagement();
  await testLicenseManagement();
  await testAuthorizationManagement();
  await testStorePages();
  await testLicenseVerification();
  await testDeviceUnbinding();
  await testOrderManagement();
  await testUserManagement();
  await testStatisticsInterfaces();

  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  console.log('📊 测试结果统计');
  console.log('='.repeat(50));
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  console.log(`📈 总计: ${testResults.total}`);
  console.log(`🎯 成功率: ${Math.round((testResults.passed / testResults.total) * 100)}%`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有测试通过！系统功能正常！');
  } else {
    console.log('\n⚠️ 有测试失败，请检查相关功能！');
    process.exit(1);
  }
}

// 运行测试
runAllTests().catch(console.error);