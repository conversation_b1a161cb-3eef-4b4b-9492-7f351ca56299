export interface CloudflareBindings {
  DB: D1Database;
  CACHE: KVNamespace;
  JWT_SECRET: string;
  API_VERSION: string;
  ALLOWED_ORIGINS: string;
}

export interface User {
  id: number;
  username: string;
  password_hash: string;
  role: 'admin' | 'distributor';
  status: 'active' | 'inactive';
  // 分发商信息
  display_name?: string;
  contact_wechat?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: number;
  name: string;
  description?: string;
  category?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface ProductVersion {
  id: number;
  product_id: number;
  version: string;
  version_name?: string;
  description?: string;
  features?: string; // JSON string
  verification_strategy: 'expiration' | 'device_count' | 'feature_based';
  max_devices?: number;
  default_price: number;
  download_link?: string;
  changelog?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface DistributorAuthorization {
  id: number;
  distributor_id: number;
  version_id: number;
  custom_price?: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface License {
  id: number;
  version_id: number;
  license_key: string;
  status: 'active' | 'expired' | 'revoked';
  expires_at?: string;
  max_devices?: number;
  features?: string;
  admin_id: number;
  order_id?: number;
  created_at: string;
  updated_at: string;
}

export interface Device {
  id: number;
  license_id: number;
  device_id: string;
  device_info?: string;
  last_verification: string;
  created_at: string;
  updated_at: string;
}

export interface VerificationLog {
  id: number;
  license_key: string;
  device_id?: string;
  result: 'success' | 'failed';
  reason?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface Order {
  id: number;
  admin_id: number;
  version_id: number;
  license_count: number;
  unit_price: number;
  total_price: number;
  status: 'pending' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface VerifyRequest {
  license_key: string;
  device_id?: string;
  product_features?: string[];
}

export interface VerifyResponse {
  success: boolean;
  message: string;
  data?: {
    license_info?: {
      product_name: string;
      expires_at?: string;
      max_devices?: number;
      current_devices: number;
      features?: string[];
    };
  };
}

export interface UserLoginRequest {
  username: string;
  password: string;
}

export interface UserLoginResponse {
  success: boolean;
  message: string;
  data?: {
    token?: string;
    admin_id?: number;
    username?: string;
    role?: string;
    authorized_products?: string[];
    expires_in?: number;
  };
}

export interface CreateProductRequest {
  name: string;
  description?: string;
  category?: string;
}

export interface CreateProductVersionRequest {
  product_id: number;
  version: string;
  version_name?: string;
  description?: string;
  features?: string[];
  verification_strategy: 'expiration' | 'device_count' | 'feature_based';
  max_devices?: number;
  default_price: number;
  download_link?: string;
  changelog?: string;
}

export interface CreateLicenseRequest {
  version_id: number;
  count?: number;
  expires_at?: string;
  max_devices?: number;
}

export interface CreateUserRequest {
  username: string;
  password: string;
  role: 'admin' | 'distributor';
  display_name?: string;
  contact_wechat?: string;
  avatar_url?: string;
}

export interface CreateDistributorAuthRequest {
  distributor_id: number;
  version_id: number;
  custom_price?: number;
}

export interface UnbindDeviceRequest {
  license_key: string;
  device_id: string;
}

export interface ApiError {
  success: false;
  message: string;
  data: null;
}

export interface ApiSuccess<T = any> {
  success: true;
  message: string;
  data?: T;
}

export type ApiResponse<T = any> = ApiSuccess<T> | ApiError;

export interface JWTPayload {
  admin_id: number;
  username: string;
  role: string;
  exp: number;
  iat: number;
  [key: string]: any;
}

export interface CacheKeys {
  licenseVerify: (key: string) => string;
  deviceBinding: (licenseId: number) => string;
  productConfig: (id: number) => string;
  adminAuth: (id: number) => string;
  salesStats: (adminId: number, period: string) => string;
}