import CryptoJS from 'crypto-js';

/**
 * 使用crypto-js的AES加密工具
 * 确保跨语言兼容性的加密实现
 */
export class UniversalEncryption {
  
  /**
   * AES加密
   * @param data 要加密的数据
   * @param key 密钥
   * @returns 加密后的字符串
   */
  static encrypt(data: string, key: string): string {
    try {
      const encrypted = CryptoJS.AES.encrypt(data, key).toString();
      return encrypted;
    } catch (error) {
      throw new Error(`加密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * AES解密
   * @param encryptedData 加密的数据
   * @param key 密钥
   * @returns 解密后的原始数据
   */
  static decrypt(encryptedData: string, key: string): string {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, key);
      const originalText = decrypted.toString(CryptoJS.enc.Utf8);
      
      if (!originalText) {
        throw new Error('解密失败或密钥错误');
      }
      
      return originalText;
    } catch (error) {
      throw new Error(`解密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 加密JSON对象
   */
  static encryptJSON(obj: any, key: string): string {
    const jsonString = JSON.stringify(obj);
    return this.encrypt(jsonString, key);
  }

  /**
   * 解密JSON对象
   */
  static decryptJSON<T = any>(encryptedData: string, key: string): T {
    const decryptedString = this.decrypt(encryptedData, key);
    return JSON.parse(decryptedString);
  }

  /**
   * 生成随机密钥
   */
  static generateRandomKey(length: number = 32): string {
    return CryptoJS.lib.WordArray.random(length/2).toString();
  }

  /**
   * 测试加解密功能
   */
  static test(): boolean {
    const testData = 'Hello World! 测试数据 {"test": 123}';
    const testKey = 'test-key-123';
    
    try {
      console.log('原始数据:', testData);
      console.log('密钥:', testKey);
      
      const encrypted = this.encrypt(testData, testKey);
      console.log('加密结果:', encrypted);
      
      const decrypted = this.decrypt(encrypted, testKey);
      console.log('解密结果:', decrypted);
      
      const success = testData === decrypted;
      console.log('测试结果:', success ? '✓ 通过' : '✗ 失败');
      
      return success;
    } catch (error) {
      console.error('测试失败:', error);
      return false;
    }
  }
}

/**
 * 配置字段加密工具
 */
export class ConfigEncryption {
  static encryptConfig(config: ConfigField[], key: string): string {
    return UniversalEncryption.encryptJSON(config, key);
  }

  static decryptConfig(encryptedConfig: string, key: string): ConfigField[] {
    return UniversalEncryption.decryptJSON<ConfigField[]>(encryptedConfig, key);
  }
}

/**
 * 配置字段接口 - 简化版本，兼容 shadcn/ui 组件自动渲染
 */
export interface ConfigField {
  key: string;                    // 字段标识
  label: string;                  // 显示标签
  type: 'string' | 'number' | 'boolean' | 'datetime' | 'array';  // 字段类型
  value: any;                     // 当前值
  default: any;                   // 默认值
  options?: Array<{               // array 类型的选项（shadcn 格式）
    label: string;
    value: string;
  }>;
}