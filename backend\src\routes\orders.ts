import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware } from '../middleware/auth';
import * as response from '../utils/response';

export function setupOrderRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // Get orders
  app.get('/api/orders', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const query = c.req.query();
      const page = parseInt(query.page || '1');
      const limit = parseInt(query.limit || '20');
      const status = query.status;
      const product_id = query.product_id ? parseInt(query.product_id) : undefined;
      const start_date = query.start_date;
      const end_date = query.end_date;
      const offset = (page - 1) * limit;
      
      let queryStr = `
        SELECT o.*, 
               pv.version, pv.version_name, pv.default_price,
               p.name as product_name, 
               u.username as user_username, u.display_name
        FROM orders o
        JOIN product_versions pv ON o.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        JOIN users u ON o.admin_id = u.id
      `;
      const params: any[] = [];
      const conditions: string[] = [];
      
      // Users can only see their own orders unless they are admin
      if (user.role !== 'admin') {
        conditions.push('o.admin_id = ?');
        params.push(user.user_id);
      }
      
      if (status) {
        conditions.push('o.status = ?');
        params.push(status);
      }
      
      if (product_id) {
        conditions.push('pv.product_id = ?');
        params.push(product_id);
      }
      
      if (start_date) {
        conditions.push('o.created_at >= ?');
        params.push(start_date);
      }
      
      if (end_date) {
        conditions.push('o.created_at <= ?');
        params.push(end_date);
      }
      
      if (conditions.length > 0) {
        queryStr += ' WHERE ' + conditions.join(' AND ');
      }
      
      queryStr += ' ORDER BY o.created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);
      
      const orders = await c.env.DB.prepare(queryStr).bind(...params).all();
      
      // Get total count
      let countQuery = `
        SELECT COUNT(*) as total 
        FROM orders o
        JOIN product_versions pv ON o.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        JOIN users u ON o.admin_id = u.id
      `;
      if (conditions.length > 0) {
        countQuery += ' WHERE ' + conditions.join(' AND ');
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      
      return response.success(c, '订单列表获取成功', {
        orders: orders.results,
        pagination: {
          page,
          limit,
          total: (countResult?.total as number) || 0,
          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),
        },
      });
    } catch (error) {
      console.error('Get orders error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Get order by ID
  app.get('/api/orders/:id', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的订单ID', 400);
      }
      
      let queryStr = `
        SELECT o.*, 
               pv.version, pv.version_name, pv.default_price,
               p.name as product_name, 
               u.username as user_username, u.display_name
        FROM orders o
        JOIN product_versions pv ON o.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        JOIN users u ON o.admin_id = u.id
        WHERE o.id = ?
      `;
      
      // Users can only see their own orders unless they are admin
      if (user.role !== 'admin') {
        queryStr += ' AND o.admin_id = ?';
      }
      
      const params = user.role === 'admin' ? [id] : [id, user.user_id];
      const order = await c.env.DB.prepare(queryStr).bind(...params).first();
      
      if (!order) {
        return response.error(c, '订单不存在或无权限访问', 404);
      }
      
      return response.success(c, '订单信息获取成功', { order });
    } catch (error) {
      console.error('Get order error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Update order status (admin only)
  app.put('/api/orders/:id', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const id = parseInt(c.req.param('id'));
      const body = await c.req.json();
      const { status } = body;
      
      if (!id || id <= 0) {
        return response.error(c, '无效的订单ID', 400);
      }
      
      if (!status) {
        return response.error(c, '状态不能为空', 400);
      }
      
      // Check if order exists and user has access
      let checkQuery = 'SELECT * FROM orders WHERE id = ?';
      if (user.role !== 'admin') {
        checkQuery += ' AND admin_id = ?';
      }
      
      const params = user.role === 'admin' ? [id] : [id, user.user_id];
      const order = await c.env.DB.prepare(checkQuery).bind(...params).first();
      
      if (!order) {
        return response.error(c, '订单不存在或无权限访问', 404);
      }
      
      await c.env.DB.prepare(`
        UPDATE orders SET status = ?, updated_at = datetime('now') WHERE id = ?
      `).bind(status, id).run();
      
      return response.success(c, '订单状态更新成功');
    } catch (error) {
      console.error('Update order error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Delete order
  app.delete('/api/orders/:id', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的订单ID', 400);
      }
      
      // Check if order exists and user has access
      let checkQuery = 'SELECT * FROM orders WHERE id = ?';
      if (user.role !== 'admin') {
        checkQuery += ' AND admin_id = ?';
      }
      
      const params = user.role === 'admin' ? [id] : [id, user.user_id];
      const order = await c.env.DB.prepare(checkQuery).bind(...params).first();
      
      if (!order) {
        return response.error(c, '订单不存在或无权限访问', 404);
      }
      
      // Only allow deletion of pending orders
      if (order.status !== 'pending') {
        return response.error(c, '只能删除待处理状态的订单', 409);
      }
      
      await c.env.DB.prepare('DELETE FROM orders WHERE id = ?').bind(id).run();
      
      return response.success(c, '订单删除成功');
    } catch (error) {
      console.error('Delete order error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
}