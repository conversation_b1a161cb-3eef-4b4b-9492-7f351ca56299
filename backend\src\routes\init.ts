import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { initializeDatabase } from '../utils/init-db';
import * as response from '../utils/response';

export function setupInitRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // 数据库初始化端点（仅在开发环境使用）
  app.post('/init-db', async (c) => {
    try {
      // 检查是否已有管理员，如果有则拒绝重复初始化
      const existingAdmin = await c.env.DB.prepare(
        'SELECT id FROM users WHERE role = ? LIMIT 1'
      ).bind('admin').first();

      if (existingAdmin) {
        return response.error(c, '数据库已初始化，无需重复操作', 400);
      }

      // 执行数据库初始化
      await initializeDatabase(c.env);

      return response.success(c, '数据库初始化成功', {
        message: '管理员账户和示例数据已创建',
        accounts: [
          { username: 'admin', password: 'password', role: 'admin' },
          { username: 'xiaoming', password: 'password', role: 'distributor' }
        ]
      });

    } catch (error) {
      console.error('Database initialization error:', error);
      return response.error(c, '数据库初始化失败', 500);
    }
  });

  // 重置数据库端点（危险操作，仅开发环境使用）
  app.post('/reset-db', async (c) => {
    try {
      await c.env.DB.prepare('DELETE FROM distributor_authorizations').run();
      await c.env.DB.prepare('DELETE FROM product_versions').run();
      await c.env.DB.prepare('DELETE FROM verification_logs').run();
      await c.env.DB.prepare('DELETE FROM devices').run();
      await c.env.DB.prepare('DELETE FROM orders').run();
      await c.env.DB.prepare('DELETE FROM licenses').run();
      await c.env.DB.prepare('DELETE FROM users').run();
      await c.env.DB.prepare('DELETE FROM products').run();

      // 重新初始化
      await initializeDatabase(c.env);

      return response.success(c, '数据库重置成功', {
        message: '所有数据已清除并重新初始化'
      });

    } catch (error) {
      console.error('Database reset error:', error);
      return response.error(c, '数据库重置失败', 500);
    }
  });

  // 数据库状态检查端点
  app.get('/db-status', async (c) => {
    try {
      const userCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM users').first();
      const productCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM products').first();
      const licenseCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM licenses').first();
      const versionCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM product_versions').first();

      return response.success(c, '数据库状态查询成功', {
        users: (userCount?.count as number) || 0,
        products: (productCount?.count as number) || 0,
        versions: (versionCount?.count as number) || 0,
        licenses: (licenseCount?.count as number) || 0,
        initialized: ((userCount?.count as number) || 0) > 0
      });

    } catch (error) {
      console.error('Database status check error:', error);
      return response.error(c, '数据库状态查询失败', 500);
    }
  });
}