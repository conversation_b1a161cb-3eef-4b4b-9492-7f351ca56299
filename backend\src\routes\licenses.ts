import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware } from '../middleware/auth';
import { generateRandomKey } from '../utils/auth';
import * as response from '../utils/response';

export function setupLicenseRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // Get licenses
  app.get('/api/licenses', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const query = c.req.query();
      const page = parseInt(query.page || '1');
      const limit = parseInt(query.limit || '20');
      const version_id = query.version_id ? parseInt(query.version_id) : undefined;
      const status = query.status;
      const search = query.search;
      const offset = (page - 1) * limit;
      
      let queryStr = `
        SELECT l.*, pv.version, pv.version_name, p.name as product_name 
        FROM licenses l 
        JOIN product_versions pv ON l.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
      `;
      const params: any[] = [];
      const conditions: string[] = [];
      
      // Distributors can only see licenses they created
      if (user.role === 'distributor') {
        conditions.push('l.admin_id = ?');
        params.push(user.user_id);
      }
      
      if (version_id) {
        conditions.push('l.version_id = ?');
        params.push(version_id);
      }
      
      if (status) {
        conditions.push('l.status = ?');
        params.push(status);
      }
      
      if (search) {
        conditions.push('l.license_key LIKE ?');
        params.push(`%${search}%`);
      }
      
      if (conditions.length > 0) {
        queryStr += ' WHERE ' + conditions.join(' AND ');
      }
      
      queryStr += ' ORDER BY l.created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);
      
      const licenses = await c.env.DB.prepare(queryStr).bind(...params).all();
      
      // Get total count
      let countQuery = 'SELECT COUNT(*) as total FROM licenses l';
      if (conditions.length > 0) {
        countQuery += ' WHERE ' + conditions.join(' AND ');
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      
      return response.success(c, '许可证列表获取成功', {
        licenses: licenses.results,
        pagination: {
          page,
          limit,
          total: (countResult?.total as number) || 0,
          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),
        },
      });
    } catch (error) {
      console.error('Get licenses error:', error);
      return response.error(c, 'Internal server error', 500);
    }
  });

  // Create licenses (generate license with automatic order creation)
  app.post('/api/licenses', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const body = await c.req.json();
      const { version_id, count = 1 } = body;
      
      if (!version_id) {
        return response.error(c, '版本ID不能为空', 400);
      }
      
      if (count <= 0 || count > 100) {
        return response.error(c, '数量必须在1到100之间', 400);
      }
      
      // Get version info and check if it exists
      const version = await c.env.DB.prepare(`
        SELECT pv.*, p.name as product_name 
        FROM product_versions pv
        JOIN products p ON pv.product_id = p.id
        WHERE pv.id = ? AND pv.status = 'active' AND p.status = 'active'
      `).bind(version_id).first();
      
      if (!version) {
        return response.error(c, '产品版本不存在或已停用', 400);
      }
      
      // Check distributor authorization
      if (user.role === 'distributor') {
        const auth = await c.env.DB.prepare(`
          SELECT * FROM distributor_authorizations 
          WHERE distributor_id = ? AND version_id = ? AND status = 'active'
        `).bind(user.user_id, version_id).first();
        
        if (!auth) {
          return response.error(c, '您没有权限销售此产品版本', 403);
        }
      }
      
      // Get pricing info
      let unitPrice = version.default_price as number;
      if (user.role === 'distributor') {
        const pricing = await c.env.DB.prepare(`
          SELECT custom_price FROM distributor_authorizations 
          WHERE distributor_id = ? AND version_id = ? AND status = 'active'
        `).bind(user.user_id, version_id).first();
        
        if (pricing?.custom_price) {
          unitPrice = pricing.custom_price as number;
        }
      }
      
      const totalPrice = unitPrice * count;
      
      try {
        // Start transaction: Create order first
        const orderResult = await c.env.DB.prepare(`
          INSERT INTO orders (admin_id, version_id, license_count, unit_price, total_price, status, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, 'completed', datetime('now'), datetime('now'))
        `).bind(user.user_id, version_id, count, unitPrice, totalPrice).run();
        
        const orderId = orderResult.meta.last_row_id;
        
        // Generate licenses with order_id
        const licenses: string[] = [];
        const insertPromises: Promise<any>[] = [];
        
        for (let i = 0; i < count; i++) {
          const licenseKey = generateRandomKey(24);
          licenses.push(licenseKey);
          
          insertPromises.push(
            c.env.DB.prepare(`
              INSERT INTO licenses (version_id, license_key, status, admin_id, order_id, created_at, updated_at)
              VALUES (?, ?, 'inactive', ?, ?, datetime('now'), datetime('now'))
            `).bind(version_id, licenseKey, user.user_id, orderId).run()
          );
        }
        
        await Promise.all(insertPromises);
        
        return response.success(c, `成功生成${count}个许可证`, { 
          licenses,
          order_id: orderId,
          total_price: totalPrice
        });
        
      } catch (error) {
        console.error('License creation transaction error:', error);
        return response.error(c, '许可证生成失败', 500);
      }
      
    } catch (error) {
      console.error('Create licenses error:', error);
      return response.error(c, 'Internal server error', 500);
    }
  });

  // Get license details
  app.get('/api/licenses/:id', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的许可证ID', 400);
      }
      
      const license = await c.env.DB.prepare(`
        SELECT l.*, pv.version, pv.version_name, pv.default_config,
               p.name as product_name, o.id as order_id, o.total_price
        FROM licenses l
        JOIN product_versions pv ON l.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        LEFT JOIN orders o ON l.order_id = o.id
        WHERE l.id = ?
      `).bind(id).first();
      
      if (!license) {
        return response.error(c, 'License not found', 404);
      }
      
      // Check permissions: distributors can only see their own licenses
      if (user.role === 'distributor' && license.admin_id !== user.user_id) {
        return response.error(c, 'Access denied to this license', 403);
      }
      
      // 获取上报记录
      const reports = await c.env.DB.prepare(`
        SELECT reported_at, created_at FROM reports 
        WHERE license_key = ? 
        ORDER BY reported_at DESC 
        LIMIT 10
      `).bind(license.license_key).all();
      
      return response.success(c, '许可证信息获取成功', {
        license,
        reports: reports.results,
      });
    } catch (error) {
      console.error('Get license error:', error);
      return response.error(c, 'Internal server error', 500);
    }
  });

  // Update license
  app.put('/api/licenses/:id', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const id = parseInt(c.req.param('id'));
      const updateData = await c.req.json();
      
      if (!id || id <= 0) {
        return response.error(c, '无效的许可证ID', 400);
      }
      
      // Check if license exists
      const license = await c.env.DB.prepare(`
        SELECT l.*, pv.id as version_id
        FROM licenses l
        JOIN product_versions pv ON l.version_id = pv.id
        WHERE l.id = ?
      `).bind(id).first();
      
      if (!license) {
        return response.error(c, 'License not found', 404);
      }
      
      // Check permissions: distributors can only update their own licenses
      if (user.role === 'distributor' && license.admin_id !== user.user_id) {
        return response.error(c, 'Access denied to this license', 403);
      }
      
      // Build update query - 只允许修改状态
      const updateFields: string[] = [];
      const params: any[] = [];
      
      if (updateData.status !== undefined) {
        // 验证状态值
        const validStatuses = ['inactive', 'active', 'revoked'];
        if (!validStatuses.includes(updateData.status)) {
          return response.error(c, '无效的状态值', 400);
        }
        updateFields.push('status = ?');
        params.push(updateData.status);
      }
      
      if (updateFields.length === 0) {
        return response.error(c, '没有要更新的字段', 400);
      }
      
      updateFields.push('updated_at = datetime(\'now\')');
      params.push(id);
      
      const query = `UPDATE licenses SET ${updateFields.join(', ')} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      
      return response.success(c, '许可证更新成功');
    } catch (error) {
      console.error('Update license error:', error);
      return response.error(c, 'Internal server error', 500);
    }
  });
}