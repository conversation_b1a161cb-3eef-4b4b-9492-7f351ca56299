/**
 * 数据库初始化脚本 - 插入用户账户和示例数据
 * 运行命令：pnpm run init-db
 */

import * as bcrypt from 'bcryptjs';

interface CloudflareBindings {
  DB: D1Database;
  CACHE: KVNamespace;
  JWT_SECRET: string;
  API_VERSION: string;
  ALLOWED_ORIGINS: string;
}

/**
 * 初始化用户账户
 */
async function initUserAccounts(DB: D1Database) {
  console.log('正在初始化用户账户...');

  try {
    // 检查管理员是否已存在
    const existingAdmin = await DB.prepare(
      'SELECT id FROM users WHERE username = ?'
    ).bind('admin').first();

    if (!existingAdmin) {
      // 创建管理员账户
      const adminPasswordHash = await bcrypt.hash('password', 10);
      
      const adminResult = await DB.prepare(`
        INSERT INTO users (username, password_hash, role, status, display_name, created_at, updated_at)
        VALUES (?, ?, 'admin', 'active', '系统管理员', datetime('now'), datetime('now'))
      `).bind('admin', adminPasswordHash).run();
      
      console.log('✅ 管理员账户创建成功 (用户名: admin, 密码: password)');
    } else {
      console.log('ℹ️ 管理员账户已存在，跳过创建');
    }

    // 检查分发商是否已存在
    const existingDistributor = await DB.prepare(
      'SELECT id FROM users WHERE username = ?'
    ).bind('xiaoming').first();

    if (!existingDistributor) {
      // 创建分发商账户
      const distributorPasswordHash = await bcrypt.hash('password', 10);
      
      const distributorResult = await DB.prepare(`
        INSERT INTO users (username, password_hash, role, status, display_name, contact_wechat, avatar_url, created_at, updated_at)
        VALUES (?, ?, 'distributor', 'active', '小明的软件店', 'xiaoming123', 'https://avatars.example.com/xiaoming.jpg', datetime('now'), datetime('now'))
      `).bind('xiaoming', distributorPasswordHash).run();
      
      console.log('✅ 分发商账户创建成功 (用户名: xiaoming, 密码: password)');
      
      // 为分发商创建授权
      const distributorId = distributorResult.meta.last_row_id;
      await createDistributorAuthorizations(DB, distributorId as number);
    } else {
      console.log('ℹ️ 分发商账户已存在，跳过创建');
    }

  } catch (error) {
    console.error('❌ 创建用户账户失败:', error);
    throw error;
  }
}

/**
 * 创建分发商授权
 */
async function createDistributorAuthorizations(DB: D1Database, distributorId: number) {
  console.log('正在创建分发商授权...');

  try {
    // 为分发商授权销售产品版本1和2，并设置自定义价格
    const authorizations = [
      { version_id: 1, custom_price: 109.00 }, // 基础版自定义价格
      { version_id: 2, custom_price: 329.00 }, // 专业版自定义价格
    ];

    for (const auth of authorizations) {
      const existing = await DB.prepare(
        'SELECT id FROM distributor_authorizations WHERE distributor_id = ? AND version_id = ?'
      ).bind(distributorId, auth.version_id).first();

      if (!existing) {
        await DB.prepare(`
          INSERT INTO distributor_authorizations (distributor_id, version_id, custom_price, status, created_at, updated_at)
          VALUES (?, ?, ?, 'active', datetime('now'), datetime('now'))
        `).bind(distributorId, auth.version_id, auth.custom_price).run();

        console.log(`✅ 分发商授权创建成功: 版本${auth.version_id}, 价格¥${auth.custom_price}`);
      }
    }

  } catch (error) {
    console.error('❌ 创建分发商授权失败:', error);
    throw error;
  }
}

/**
 * 插入示例许可证和订单数据
 */
async function insertSampleData(DB: D1Database) {
  console.log('正在插入示例许可证和订单数据...');

  try {
    // 获取用户ID
    const admin = await DB.prepare('SELECT id FROM users WHERE username = ?').bind('admin').first();
    const distributor = await DB.prepare('SELECT id FROM users WHERE username = ?').bind('xiaoming').first();

    if (!admin || !distributor) {
      throw new Error('用户账户不存在，请先运行用户初始化');
    }

    // 创建示例订单和许可证
    const sampleData = [
      {
        user_id: admin.id,
        version_id: 1, // MyAwesomeApp 基础版
        license_count: 1,
        unit_price: 99.00,
        license_key: 'DEMO-BASIC-001-ABCD-EFGH',
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1年后过期
      },
      {
        user_id: distributor.id,
        version_id: 2, // MyAwesomeApp 专业版
        license_count: 1,
        unit_price: 329.00, // 分发商自定义价格
        license_key: 'DEMO-PRO-002-IJKL-MNOP',
        expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
      },
    ];

    for (const data of sampleData) {
      // 检查许可证是否已存在
      const existingLicense = await DB.prepare('SELECT id FROM licenses WHERE license_key = ?')
        .bind(data.license_key).first();

      if (!existingLicense) {
        // 创建订单
        const orderResult = await DB.prepare(`
          INSERT INTO orders (admin_id, version_id, license_count, unit_price, total_price, status, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, 'completed', datetime('now'), datetime('now'))
        `).bind(
          data.user_id,
          data.version_id,
          data.license_count,
          data.unit_price,
          data.unit_price * data.license_count
        ).run();

        const orderId = orderResult.meta.last_row_id;

        // 创建许可证
        await DB.prepare(`
          INSERT INTO licenses (version_id, license_key, status, expires_at, max_devices, admin_id, order_id, created_at, updated_at)
          VALUES (?, ?, 'active', ?, NULL, ?, ?, datetime('now'), datetime('now'))
        `).bind(
          data.version_id,
          data.license_key,
          data.expires_at,
          data.user_id,
          orderId
        ).run();

        console.log(`✅ 示例数据创建成功: ${data.license_key}`);
      } else {
        console.log(`ℹ️ 许可证已存在，跳过创建: ${data.license_key}`);
      }
    }

  } catch (error) {
    console.error('❌ 插入示例数据失败:', error);
    throw error;
  }
}

/**
 * 主初始化函数
 */
export async function initializeDatabase(env: CloudflareBindings) {
  console.log('🚀 开始初始化数据库...');
  
  try {
    // 1. 初始化用户账户
    await initUserAccounts(env.DB);
    
    // 2. 插入示例数据
    await insertSampleData(env.DB);
    
    console.log('🎉 数据库初始化完成！');
    console.log('');
    console.log('账户信息：');
    console.log('管理员   - 用户名: admin, 密码: password');
    console.log('分发商   - 用户名: xiaoming, 密码: password');
    console.log('');
    
  } catch (error) {
    console.error('💥 数据库初始化失败:', error);
    throw error;
  }
}