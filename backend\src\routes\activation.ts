import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { UniversalEncryption, ConfigField } from '../utils/encryption';
import * as response from '../utils/response';

export function setupActivationRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  
  // 获取默认配置模板
  app.get('/api/config/:version_id', async (c) => {
    try {
      const versionId = parseInt(c.req.param('version_id'));
      
      if (!versionId || versionId <= 0) {
        return response.error(c, '无效的版本ID', 400);
      }
      
      // 获取版本信息和默认配置
      const version = await c.env.DB.prepare(`
        SELECT pv.*, p.name as product_name
        FROM product_versions pv
        JOIN products p ON pv.product_id = p.id
        WHERE pv.id = ? AND pv.status = 'active' AND p.status = 'active'
      `).bind(versionId).first();
      
      if (!version) {
        return response.error(c, '版本不存在或已停用', 404);
      }
      
      // 返回默认配置模板（不包含加密密钥）
      const defaultConfig = JSON.parse(version.default_config as string);
      
      return response.success(c, '默认配置获取成功', {
        version_info: {
          id: version.id,
          version: version.version,
          version_name: version.version_name,
          product_name: version.product_name,
        },
        default_config: defaultConfig
      });
      
    } catch (error) {
      console.error('Get default config error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // License激活接口
  app.post('/api/activate', async (c) => {
    try {
      const body = await c.req.json();
      const { license_key, client_config } = body;
      
      if (!license_key) {
        return response.error(c, '许可证密钥不能为空', 400);
      }
      
      if (!client_config || !Array.isArray(client_config)) {
        return response.error(c, '客户端配置格式错误', 400);
      }
      
      // 查找许可证
      const license = await c.env.DB.prepare(`
        SELECT l.*, pv.default_config, pv.encryption_key, 
               p.name as product_name, pv.version, pv.version_name
        FROM licenses l
        JOIN product_versions pv ON l.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        WHERE l.license_key = ?
      `).bind(license_key).first();
      
      if (!license) {
        return response.error(c, '许可证不存在', 404);
      }
      
      if (license.status === 'revoked') {
        return response.error(c, '许可证已被撤销', 403);
      }
      
      if (license.status === 'active') {
        // 已激活的许可证，返回现有配置
        const existingActivation = await c.env.DB.prepare(`
          SELECT server_config FROM licenses WHERE id = ?
        `).bind(license.id).first();
        
        if (existingActivation?.server_config) {
          try {
            const serverConfig = UniversalEncryption.decryptJSON(
              existingActivation.server_config as string, 
              license.encryption_key as string
            );
            
            return response.success(c, '许可证已激活', {
              license_info: {
                product_name: license.product_name,
                version: license.version,
                version_name: license.version_name,
                status: license.status
              },
              server_config: serverConfig
            });
          } catch (decryptError) {
            console.error('Decrypt existing config error:', decryptError);
            // 如果解密失败，继续重新激活流程
          }
        }
      }
      
      // 解析默认配置
      const defaultConfig: ConfigField[] = JSON.parse(license.default_config as string);
      
      // 基于许可证类型修改默认配置
      const serverConfig = modifyConfigForLicense(defaultConfig, license);
      
      // 加密服务器配置
      const encryptedServerConfig = UniversalEncryption.encryptJSON(
        serverConfig, 
        license.encryption_key as string
      );
      
      // 加密客户端配置用于存储
      const encryptedClientConfig = UniversalEncryption.encryptJSON(
        client_config, 
        license.encryption_key as string
      );
      
      // 更新许可证状态和配置
      await c.env.DB.prepare(`
        UPDATE licenses 
        SET status = 'active', server_config = ?, updated_at = datetime('now')
        WHERE id = ?
      `).bind(encryptedServerConfig, license.id).run();
      
      // 插入/更新上报记录
      await c.env.DB.prepare(`
        INSERT OR REPLACE INTO reports (license_key, client_data, reported_at, updated_at)
        VALUES (?, ?, datetime('now'), datetime('now'))
      `).bind(license_key, encryptedClientConfig).run();
      
      return response.success(c, '许可证激活成功', {
        license_info: {
          product_name: license.product_name,
          version: license.version,
          version_name: license.version_name,
          status: 'active'
        },
        server_config: serverConfig
      });
      
    } catch (error) {
      console.error('License activation error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
  
  // 获取许可证配置（用于本地比对）
  app.post('/api/get-config', async (c) => {
    try {
      const body = await c.req.json();
      const { license_key } = body;
      
      if (!license_key) {
        return response.error(c, '许可证密钥不能为空', 400);
      }
      
      // 查找许可证
      const license = await c.env.DB.prepare(`
        SELECT l.*, pv.encryption_key, p.name as product_name, pv.version, pv.version_name
        FROM licenses l
        JOIN product_versions pv ON l.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        WHERE l.license_key = ? AND l.status = 'active'
      `).bind(license_key).first();
      
      if (!license) {
        return response.error(c, '许可证不存在或未激活', 404);
      }
      
      if (!license.server_config) {
        return response.error(c, '许可证未完成激活', 400);
      }
      
      // 解密服务器配置
      const serverConfig = UniversalEncryption.decryptJSON(
        license.server_config as string, 
        license.encryption_key as string
      );
      
      // 获取最新的客户端上报数据
      const latestReport = await c.env.DB.prepare(`
        SELECT client_data FROM reports 
        WHERE license_key = ? 
        ORDER BY reported_at DESC 
        LIMIT 1
      `).bind(license_key).first();
      
      let clientData = null;
      if (latestReport?.client_data) {
        try {
          clientData = UniversalEncryption.decryptJSON(
            latestReport.client_data as string,
            license.encryption_key as string
          );
        } catch (error) {
          console.error('Decrypt client data error:', error);
        }
      }
      
      return response.success(c, '配置获取成功', {
        license_info: {
          product_name: license.product_name,
          version: license.version,
          version_name: license.version_name,
        },
        server_config: serverConfig,
        client_data: clientData
      });
      
    } catch (error) {
      console.error('Get config error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
  
  // 上报客户端数据
  app.post('/api/report', async (c) => {
    try {
      const body = await c.req.json();
      const { license_key, client_data } = body;
      
      if (!license_key || !client_data) {
        return response.error(c, '许可证密钥和客户端数据不能为空', 400);
      }
      
      // 验证许可证存在且已激活
      const license = await c.env.DB.prepare(`
        SELECT l.id, pv.encryption_key
        FROM licenses l
        JOIN product_versions pv ON l.version_id = pv.id
        WHERE l.license_key = ? AND l.status = 'active'
      `).bind(license_key).first();
      
      if (!license) {
        return response.error(c, '许可证不存在或未激活', 404);
      }
      
      // 加密客户端数据
      const encryptedClientData = UniversalEncryption.encryptJSON(
        client_data, 
        license.encryption_key as string
      );
      
      // 插入/更新上报记录
      await c.env.DB.prepare(`
        INSERT OR REPLACE INTO reports (license_key, client_data, reported_at, updated_at)
        VALUES (?, ?, datetime('now'), datetime('now'))
      `).bind(license_key, encryptedClientData).run();
      
      return response.success(c, '数据上报成功');
      
    } catch (error) {
      console.error('Report data error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  /**
   * 根据许可证类型修改配置
   */
  function modifyConfigForLicense(defaultConfig: ConfigField[], license: any): ConfigField[] {
    const serverConfig = JSON.parse(JSON.stringify(defaultConfig)); // 深拷贝
    
    // 根据版本名称或其他条件修改配置
    for (const field of serverConfig) {
      switch (field.key) {
        case 'expires_at':
          // 设置过期时间（例如：基础版1年，专业版2年）
          const now = new Date();
          if (license.version_name?.includes('专业')) {
            now.setFullYear(now.getFullYear() + 2);
          } else {
            now.setFullYear(now.getFullYear() + 1);
          }
          field.value = now.toISOString();
          break;
          
        case 'max_devices':
          // 设置设备数限制
          if (license.version_name?.includes('专业')) {
            field.value = 5;
          } else {
            field.value = 1;
          }
          break;
          
        case 'features':
          // 保持默认功能列表
          field.value = field.default;
          break;
          
        default:
          // 其他字段保持默认值
          field.value = field.default;
          break;
      }
    }
    
    return serverConfig;
  }
}