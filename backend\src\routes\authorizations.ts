import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware, adminOnly } from '../middleware/auth';
import * as response from '../utils/response';

export function setupAuthorizationRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // =============================
  // 分发商授权管理接口 (Distributor Authorizations)
  // =============================
  
  // Get all distributor authorizations (admin only)
  app.get('/api/authorizations', authMiddleware(), adminOnly(), async (c) => {
    try {
      const query = c.req.query();
      const page = parseInt(query.page || '1');
      const limit = parseInt(query.limit || '20');
      const distributorId = query.distributor_id;
      const versionId = query.version_id;
      const status = query.status;
      const offset = (page - 1) * limit;
      
      let queryStr = `
        SELECT da.*, 
               u.username as distributor_username, 
               u.display_name as distributor_name,
               pv.version, pv.version_name, pv.default_price,
               p.name as product_name
        FROM distributor_authorizations da
        JOIN users u ON da.distributor_id = u.id
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
      `;
      const params: any[] = [];
      const conditions: string[] = [];
      
      if (distributorId) {
        conditions.push('da.distributor_id = ?');
        params.push(distributorId);
      }
      
      if (versionId) {
        conditions.push('da.version_id = ?');
        params.push(versionId);
      }
      
      if (status) {
        conditions.push('da.status = ?');
        params.push(status);
      }
      
      if (conditions.length > 0) {
        queryStr += ' WHERE ' + conditions.join(' AND ');
      }
      
      queryStr += ' ORDER BY da.created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);
      
      const authorizations = await c.env.DB.prepare(queryStr).bind(...params).all();
      
      // Get total count
      let countQuery = `
        SELECT COUNT(*) as total 
        FROM distributor_authorizations da
        JOIN users u ON da.distributor_id = u.id
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
      `;
      if (conditions.length > 0) {
        countQuery += ' WHERE ' + conditions.join(' AND ');
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      
      return response.success(c, '分发商授权列表获取成功', {
        authorizations: authorizations.results,
        pagination: {
          page,
          limit,
          total: (countResult?.total as number) || 0,
          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),
        },
      });
    } catch (error) {
      console.error('Get distributor authorizations error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Create distributor authorization (admin only)
  app.post('/api/authorizations', authMiddleware(), adminOnly(), async (c) => {
    try {
      const body = await c.req.json();
      const { distributor_id, version_id, custom_price } = body;
      
      if (!distributor_id || !version_id) {
        return response.error(c, '分发商ID和版本ID不能为空', 400);
      }
      
      // Check if distributor exists and is a distributor
      const distributor = await c.env.DB.prepare('SELECT id, role FROM users WHERE id = ? AND role = \'distributor\' AND status = \'active\'').bind(distributor_id).first();
      if (!distributor) {
        return response.error(c, '分发商不存在或已停用', 400);
      }
      
      // Check if product version exists
      const version = await c.env.DB.prepare('SELECT id, default_price FROM product_versions WHERE id = ? AND status = \'active\'').bind(version_id).first();
      if (!version) {
        return response.error(c, '产品版本不存在或已停用', 400);
      }
      
      // Check if authorization already exists
      const existing = await c.env.DB.prepare('SELECT id FROM distributor_authorizations WHERE distributor_id = ? AND version_id = ?').bind(distributor_id, version_id).first();
      if (existing) {
        return response.error(c, '该分发商已被授权销售此版本', 400);
      }
      
      const result = await c.env.DB.prepare(`
        INSERT INTO distributor_authorizations (distributor_id, version_id, custom_price, status, created_at, updated_at)
        VALUES (?, ?, ?, 'active', datetime('now'), datetime('now'))
      `).bind(distributor_id, version_id, custom_price || null).run();
      
      return response.success(c, '分发商授权创建成功', { id: result.meta.last_row_id });
    } catch (error) {
      console.error('Create distributor authorization error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Update distributor authorization (admin only)
  app.put('/api/authorizations/:id', authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      const updateData = await c.req.json();
      
      if (!id || id <= 0) {
        return response.error(c, '无效的授权ID', 400);
      }
      
      // Check if authorization exists
      const existing = await c.env.DB.prepare('SELECT id FROM distributor_authorizations WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '授权不存在', 404);
      }
      
      // Build update query
      const updateFields: string[] = [];
      const params: any[] = [];
      
      if (updateData.custom_price !== undefined) {
        updateFields.push('custom_price = ?');
        params.push(updateData.custom_price);
      }
      if (updateData.status !== undefined) {
        updateFields.push('status = ?');
        params.push(updateData.status);
      }
      
      if (updateFields.length === 0) {
        return response.error(c, '没有要更新的字段', 400);
      }
      
      updateFields.push('updated_at = datetime(\'now\')');
      params.push(id);
      
      const query = `UPDATE distributor_authorizations SET ${updateFields.join(', ')} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      
      return response.success(c, '分发商授权更新成功');
    } catch (error) {
      console.error('Update distributor authorization error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Delete distributor authorization (admin only)
  app.delete('/api/authorizations/:id', authMiddleware(), adminOnly(), async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的授权ID', 400);
      }
      
      // Check if authorization exists
      const existing = await c.env.DB.prepare('SELECT id FROM distributor_authorizations WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '授权不存在', 404);
      }
      
      await c.env.DB.prepare('DELETE FROM distributor_authorizations WHERE id = ?').bind(id).run();
      
      return response.success(c, '分发商授权删除成功');
    } catch (error) {
      console.error('Delete distributor authorization error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Get authorized versions for a distributor
  app.get('/api/distributors/:id/authorizations', authMiddleware(), async (c) => {
    try {
      const distributorId = parseInt(c.req.param('id'));
      const currentUser = c.get('user');
      
      if (!distributorId || distributorId <= 0) {
        return response.error(c, '无效的分发商ID', 400);
      }
      
      // Check permissions: admin can view any distributor, distributors can only view themselves
      if (currentUser.role !== 'admin' && currentUser.user_id !== distributorId) {
        return response.error(c, '权限不足', 403);
      }
      
      // Check if distributor exists
      const distributor = await c.env.DB.prepare('SELECT id, username, display_name FROM users WHERE id = ? AND role = \'distributor\'').bind(distributorId).first();
      if (!distributor) {
        return response.error(c, '分发商不存在', 404);
      }
      
      const authorizations = await c.env.DB.prepare(`
        SELECT da.id as authorization_id, da.custom_price, da.status as auth_status,
               pv.id as version_id, pv.version, pv.version_name, pv.description, 
               pv.default_price, pv.download_link, pv.changelog, pv.features,
               p.id as product_id, p.name as product_name, p.category
        FROM distributor_authorizations da
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        WHERE da.distributor_id = ? AND da.status = 'active'
        ORDER BY p.name, pv.version
      `).bind(distributorId).all();
      
      return response.success(c, '分发商授权版本获取成功', {
        distributor,
        authorizations: authorizations.results,
      });
    } catch (error) {
      console.error('Get distributor authorizations error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
}