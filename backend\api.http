### License Verification Service API Testing
### Base URL: http://localhost:8787 (development) or your deployed worker URL

@baseUrl = http://localhost:8787
@refreshToken =

# @name healthCheck
GET {{baseUrl}}/
Content-Type: application/json

###
# @name initDatabase
POST {{baseUrl}}/init-db
Content-Type: application/json

### === Authentication ===

# @name adminLogin
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "username": "root",
  "password": "password"
}
###
# @name refreshToken
POST {{baseUrl}}/api/auth/refresh
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}
###
# @name adminLogout
POST {{baseUrl}}/api/auth/logout
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}

### === Products (Super Admin Only) ===

###
# @name getAllProducts
GET {{baseUrl}}/api/products?page=1&limit=20&status=active&search=
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name createProduct
POST {{baseUrl}}/api/products
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}

{
  "name": "My Software v1.0",
  "description": "Test software product",
  "verification_strategy": "device_count",
  "max_devices": 3,
  "features": ["feature1", "feature2", "premium"]
}

###
# @name getProductById
GET {{baseUrl}}/api/products/{{createProduct.response.body.data.id}}
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name updateProduct
PUT {{baseUrl}}/api/products/{{createProduct.response.body.data.id}}
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}

{
  "name": "Updated Software v1.1",
  "description": "Updated description",
  "max_devices": 5,
  "status": "active"
}

###
# @name deleteProduct
DELETE {{baseUrl}}/api/products/{{createProduct.response.body.data.id}}
Authorization: Bearer {{adminLogin.response.body.data.token}}

### === Administrators (Super Admin Only) ===

###
# @name getAllAdmins
GET {{baseUrl}}/api/admins?page=1&limit=20&role=normal
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name createAdmin
POST {{baseUrl}}/api/admins
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}

{
  "username": "testadmin",
  "password": "testpassword",
  "role": "normal",
  "product_ids": [{{createProduct.response.body.data.id}}]
}

###
# @name getAdminById
GET {{baseUrl}}/api/admins/{{createAdmin.response.body.data.id}}
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name updateAdmin
PUT {{baseUrl}}/api/admins/{{createAdmin.response.body.data.id}}
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}

{
  "product_ids": [{{createProduct.response.body.data.id}}],
  "status": "active"
}

###
# @name deleteAdmin
DELETE {{baseUrl}}/api/admins/{{createAdmin.response.body.data.id}}
Authorization: Bearer {{adminLogin.response.body.data.token}}

### === Licenses ===

###
# @name getAllLicenses
GET {{baseUrl}}/api/v1/licenses?page=1&limit=20&status=active&product_id={{createProduct.response.body.data.id}}
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name createLicenses
POST {{baseUrl}}/api/v1/licenses
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}

{
  "product_id": 1,
  "count": 5,
  "expires_at": "2025-12-31T23:59:59.000Z",
  "max_devices": 3
}

###
# @name getLicenseById
GET {{baseUrl}}/api/v1/licenses/1
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name updateLicense
PUT {{baseUrl}}/api/v1/licenses/1
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}

{
  "expires_at": "2026-12-31T23:59:59.000Z",
  "max_devices": 5,
  "status": "active"
}

### === Orders ===

###
# @name getAllOrders
### Get All Orders
GET {{baseUrl}}/api/v1/orders?page=1&limit=20&status=completed&product_id={{createProduct.response.body.data.id}}&start_date=2024-01-01&end_date=2025-12-31
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name createOrder
POST {{baseUrl}}/api/v1/orders
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}

{
  "product_id": 1,
  "license_count": 10,
  "unit_price": 99.99
}

###
# @name getOrderById
GET {{baseUrl}}/api/v1/orders/{{createOrder.response.body.data.id}}
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name updateOrderStatus
PUT {{baseUrl}}/api/v1/orders/{{createOrder.response.body.data.id}}
Content-Type: application/json
Authorization: Bearer {{adminLogin.response.body.data.token}}

{
  "status": "completed"
}

###
# @name deleteOrder
DELETE {{baseUrl}}/api/v1/orders/{{createOrder.response.body.data.id}}
Authorization: Bearer {{adminLogin.response.body.data.token}}

### === Statistics ===

###
# @name getSalesStats
GET {{baseUrl}}/api/v1/stats/sales?period=month&start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name getVerificationStats
GET {{baseUrl}}/api/v1/stats/verification?period=week
Authorization: Bearer {{adminLogin.response.body.data.token}}

###
# @name getDashboardOverviewrview
GET {{baseUrl}}/api/v1/stats/dashboard
Authorization: Bearer {{adminLogin.response.body.data.token}}

### === Client Endpoints (Public) ===

###
# @name verifyLicense
### Verify License
POST {{baseUrl}}/verify
Content-Type: application/json

{
  "license_key": "63979BOLQ3WK5YAUB92WGHE2",
  "device_id": "unique-device-identifier",
  "product_features": ["feature1", "premium"]
}

###
# @name unbindDevice
POST {{baseUrl}}/device/unbind
Content-Type: application/json

{
  "license_key": "{{createLicenses.response.body.data.licenses[0]}}",
  "device_id": "unique-device-identifier"
}
