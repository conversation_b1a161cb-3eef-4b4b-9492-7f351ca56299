import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware, adminOnly } from '../middleware/auth';
import * as bcrypt from 'bcryptjs';
import * as response from '../utils/response';

export function setupUserRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // Apply middleware for all user management routes (admin only)
  app.use('/api/users', authMiddleware(), adminOnly());
  app.use('/api/users/*', authMiddleware(), adminOnly());

  // Get all users (admin only)
  app.get('/api/users', async (c) => {
    try {
      const query = c.req.query();
      const page = parseInt(query.page || '1');
      const limit = parseInt(query.limit || '20');
      const role = query.role;
      const status = query.status;
      const search = query.search;
      const offset = (page - 1) * limit;
      
      let queryStr = 'SELECT id, username, role, status, display_name, contact_wechat, avatar_url, created_at, updated_at FROM users';
      const params: any[] = [];
      const conditions: string[] = [];
      
      if (role) {
        conditions.push('role = ?');
        params.push(role);
      }
      
      if (status) {
        conditions.push('status = ?');
        params.push(status);
      }
      
      if (search) {
        conditions.push('(username LIKE ? OR display_name LIKE ?)');
        params.push(`%${search}%`, `%${search}%`);
      }
      
      if (conditions.length > 0) {
        queryStr += ' WHERE ' + conditions.join(' AND ');
      }
      
      queryStr += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);
      
      const users = await c.env.DB.prepare(queryStr).bind(...params).all();
      
      // Get total count
      let countQuery = 'SELECT COUNT(*) as total FROM users';
      if (conditions.length > 0) {
        countQuery += ' WHERE ' + conditions.join(' AND ');
      }
      const countResult = await c.env.DB.prepare(countQuery).bind(...params.slice(0, -2)).first();
      
      return response.success(c, '用户列表获取成功', {
        users: users.results,
        pagination: {
          page,
          limit,
          total: (countResult?.total as number) || 0,
          totalPages: Math.ceil(((countResult?.total as number) || 0) / limit),
        },
      });
    } catch (error) {
      console.error('Get users error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Create user
  app.post('/api/users', async (c) => {
    try {
      const body = await c.req.json();
      const { username, password, role, display_name, contact_wechat, avatar_url } = body;
      
      if (!username || !password || !role) {
        return response.error(c, '用户名、密码和角色不能为空', 400);
      }
      
      if (!['admin', 'distributor'].includes(role)) {
        return response.error(c, '角色必须是 admin 或 distributor', 400);
      }
      
      // Check if username already exists
      const existing = await c.env.DB.prepare('SELECT id FROM users WHERE username = ?').bind(username).first();
      if (existing) {
        return response.error(c, '用户名已存在', 400);
      }
      
      // Hash password
      const passwordHash = await bcrypt.hash(password, 10);
      
      const result = await c.env.DB.prepare(`
        INSERT INTO users (username, password_hash, role, status, display_name, contact_wechat, avatar_url, created_at, updated_at)
        VALUES (?, ?, ?, 'active', ?, ?, ?, datetime('now'), datetime('now'))
      `).bind(username, passwordHash, role, display_name || null, contact_wechat || null, avatar_url || null).run();
      
      return response.success(c, '用户创建成功', { id: result.meta.last_row_id });
    } catch (error) {
      console.error('Create user error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Get user by ID
  app.get('/api/users/:id', async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的用户ID', 400);
      }
      
      const user = await c.env.DB.prepare(`
        SELECT id, username, role, status, display_name, contact_wechat, avatar_url, created_at, updated_at 
        FROM users 
        WHERE id = ?
      `).bind(id).first();
      
      if (!user) {
        return response.error(c, '用户不存在', 404);
      }
      
      // Get authorized versions for distributors
      let authorizedVersions = [];
      if (user.role === 'distributor') {
        const authorizations = await c.env.DB.prepare(`
          SELECT da.version_id, da.custom_price, pv.version, pv.version_name, pv.default_price,
                 p.id as product_id, p.name as product_name
          FROM distributor_authorizations da
          JOIN product_versions pv ON da.version_id = pv.id
          JOIN products p ON pv.product_id = p.id
          WHERE da.distributor_id = ? AND da.status = 'active'
          ORDER BY p.name, pv.version
        `).bind(id).all();
        
        authorizedVersions = authorizations.results;
      }
      
      return response.success(c, '用户信息获取成功', {
        user: {
          ...user,
          authorized_versions: authorizedVersions,
        },
      });
    } catch (error) {
      console.error('Get user error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Update user
  app.put('/api/users/:id', async (c) => {
    try {
      const id = parseInt(c.req.param('id'));
      const updateData = await c.req.json();
      
      if (!id || id <= 0) {
        return response.error(c, '无效的用户ID', 400);
      }
      
      // Check if user exists
      const existing = await c.env.DB.prepare('SELECT id FROM users WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '用户不存在', 404);
      }
      
      // Build update query
      const updateFields: string[] = [];
      const params: any[] = [];
      
      if (updateData.password !== undefined) {
        const passwordHash = await bcrypt.hash(updateData.password, 10);
        updateFields.push('password_hash = ?');
        params.push(passwordHash);
      }
      if (updateData.role !== undefined) {
        if (!['admin', 'distributor'].includes(updateData.role)) {
          return response.error(c, '角色必须是 admin 或 distributor', 400);
        }
        updateFields.push('role = ?');
        params.push(updateData.role);
      }
      if (updateData.status !== undefined) {
        updateFields.push('status = ?');
        params.push(updateData.status);
      }
      if (updateData.display_name !== undefined) {
        updateFields.push('display_name = ?');
        params.push(updateData.display_name);
      }
      if (updateData.contact_wechat !== undefined) {
        updateFields.push('contact_wechat = ?');
        params.push(updateData.contact_wechat);
      }
      if (updateData.avatar_url !== undefined) {
        updateFields.push('avatar_url = ?');
        params.push(updateData.avatar_url);
      }
      
      if (updateFields.length === 0) {
        return response.error(c, '没有要更新的字段', 400);
      }
      
      updateFields.push('updated_at = datetime(\'now\')');
      params.push(id);
      
      const query = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;
      await c.env.DB.prepare(query).bind(...params).run();
      
      // Clear cache for this user
      const cacheKey = `user:auth:${id}`;
      await c.env.CACHE.delete(cacheKey);
      
      return response.success(c, '用户更新成功');
    } catch (error) {
      console.error('Update user error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Delete user
  app.delete('/api/users/:id', async (c) => {
    try {
      const currentUser = c.get('admin');
      const id = parseInt(c.req.param('id'));
      
      if (!id || id <= 0) {
        return response.error(c, '无效的用户ID', 400);
      }
      
      // Prevent self-deletion
      if (currentUser.admin_id === id) {
        return response.error(c, '不能删除自己的账号', 400);
      }
      
      // Check if user exists
      const existing = await c.env.DB.prepare('SELECT id, role FROM users WHERE id = ?').bind(id).first();
      if (!existing) {
        return response.error(c, '用户不存在', 404);
      }
      
      // Check if user has active licenses
      const activeLicenses = await c.env.DB.prepare('SELECT COUNT(*) as count FROM licenses WHERE admin_id = ? AND status = \'active\'').bind(id).first();
      if (activeLicenses && (activeLicenses.count as number) > 0) {
        return response.error(c, '不能删除拥有活跃许可证的用户', 409);
      }
      
      // If it's a distributor, also delete their authorizations
      if (existing.role === 'distributor') {
        await c.env.DB.prepare('DELETE FROM distributor_authorizations WHERE distributor_id = ?').bind(id).run();
      }
      
      await c.env.DB.prepare('DELETE FROM users WHERE id = ?').bind(id).run();
      
      // Clear cache
      const cacheKey = `user:auth:${id}`;
      await c.env.CACHE.delete(cacheKey);
      
      return response.success(c, '用户删除成功');
    } catch (error) {
      console.error('Delete user error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
}