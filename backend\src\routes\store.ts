import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import * as response from '../utils/response';

export function setupStoreRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // =============================
  // 店铺页面接口 (Store Page APIs)
  // =============================
  
  // Get distributor store page (public access)
  app.get('/api/store/:username', async (c) => {
    try {
      const username = c.req.param('username');
      
      if (!username) {
        return response.error(c, '用户名不能为空', 400);
      }
      
      // Get distributor info
      const distributor = await c.env.DB.prepare(`
        SELECT id, username, display_name, contact_wechat, avatar_url
        FROM users 
        WHERE username = ? AND role = 'distributor' AND status = 'active'
      `).bind(username).first();
      
      if (!distributor) {
        return response.error(c, '店铺不存在', 404);
      }
      
      // Get authorized products and versions
      const products = await c.env.DB.prepare(`
        SELECT da.custom_price,
               pv.id as version_id, pv.version, pv.version_name, pv.description, 
               pv.default_price, pv.features, pv.changelog,
               p.id as product_id, p.name as product_name, p.category, p.description as product_description
        FROM distributor_authorizations da
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        WHERE da.distributor_id = ? AND da.status = 'active' AND pv.status = 'active' AND p.status = 'active'
        ORDER BY p.name, pv.version
      `).bind(distributor.id).all();
      
      // Group by product
      const groupedProducts: any = {};
      products.results.forEach((item: any) => {
        if (!groupedProducts[item.product_id]) {
          groupedProducts[item.product_id] = {
            id: item.product_id,
            name: item.product_name,
            description: item.product_description,
            category: item.category,
            versions: [],
          };
        }
        
        groupedProducts[item.product_id].versions.push({
          id: item.version_id,
          version: item.version,
          version_name: item.version_name,
          description: item.description,
          features: item.features ? JSON.parse(item.features) : [],
          changelog: item.changelog,
          price: item.custom_price || item.default_price,
          original_price: item.default_price,
        });
      });
      
      return response.success(c, '店铺信息获取成功', {
        store: {
          distributor: {
            username: distributor.username,
            display_name: distributor.display_name,
            contact_wechat: distributor.contact_wechat,
            avatar_url: distributor.avatar_url,
          },
          products: Object.values(groupedProducts),
        },
      });
    } catch (error) {
      console.error('Get store page error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Get store products list (with search and filtering)
  app.get('/api/store/:username/products', async (c) => {
    try {
      const username = c.req.param('username');
      const query = c.req.query();
      const category = query.category;
      const search = query.search;
      
      if (!username) {
        return response.error(c, '用户名不能为空', 400);
      }
      
      // Get distributor info
      const distributor = await c.env.DB.prepare(`
        SELECT id, username, display_name
        FROM users 
        WHERE username = ? AND role = 'distributor' AND status = 'active'
      `).bind(username).first();
      
      if (!distributor) {
        return response.error(c, '店铺不存在', 404);
      }
      
      // Build query with filters
      let queryStr = `
        SELECT da.custom_price,
               pv.id as version_id, pv.version, pv.version_name, pv.description, 
               pv.default_price, pv.features,
               p.id as product_id, p.name as product_name, p.category, p.description as product_description
        FROM distributor_authorizations da
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        WHERE da.distributor_id = ? AND da.status = 'active' AND pv.status = 'active' AND p.status = 'active'
      `;
      const params: any[] = [distributor.id];
      
      if (category) {
        queryStr += ' AND p.category = ?';
        params.push(category);
      }
      
      if (search) {
        queryStr += ' AND (p.name LIKE ? OR p.description LIKE ? OR pv.version_name LIKE ?)';
        params.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }
      
      queryStr += ' ORDER BY p.name, pv.version';
      
      const products = await c.env.DB.prepare(queryStr).bind(...params).all();
      
      // Group by product
      const groupedProducts: any = {};
      products.results.forEach((item: any) => {
        if (!groupedProducts[item.product_id]) {
          groupedProducts[item.product_id] = {
            id: item.product_id,
            name: item.product_name,
            description: item.product_description,
            category: item.category,
            versions: [],
          };
        }
        
        groupedProducts[item.product_id].versions.push({
          id: item.version_id,
          version: item.version,
          version_name: item.version_name,
          description: item.description,
          features: item.features ? JSON.parse(item.features) : [],
          price: item.custom_price || item.default_price,
          original_price: item.default_price,
        });
      });
      
      return response.success(c, '店铺商品列表获取成功', {
        products: Object.values(groupedProducts),
      });
    } catch (error) {
      console.error('Get store products error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });

  // Get single product in store
  app.get('/api/store/:username/products/:productId', async (c) => {
    try {
      const username = c.req.param('username');
      const productId = parseInt(c.req.param('productId'));
      
      if (!username) {
        return response.error(c, '用户名不能为空', 400);
      }
      
      if (!productId || productId <= 0) {
        return response.error(c, '无效的产品ID', 400);
      }
      
      // Get distributor info
      const distributor = await c.env.DB.prepare(`
        SELECT id, username, display_name, contact_wechat, avatar_url
        FROM users 
        WHERE username = ? AND role = 'distributor' AND status = 'active'
      `).bind(username).first();
      
      if (!distributor) {
        return response.error(c, '店铺不存在', 404);
      }
      
      // Get product with versions
      const versions = await c.env.DB.prepare(`
        SELECT da.custom_price,
               pv.id as version_id, pv.version, pv.version_name, pv.description, 
               pv.default_price, pv.features, pv.changelog,
               p.id as product_id, p.name as product_name, p.category, p.description as product_description
        FROM distributor_authorizations da
        JOIN product_versions pv ON da.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        WHERE da.distributor_id = ? AND p.id = ? AND da.status = 'active' AND pv.status = 'active' AND p.status = 'active'
        ORDER BY pv.version DESC
      `).bind(distributor.id, productId).all();
      
      if (!versions.results.length) {
        return response.error(c, '商品不存在或未授权', 404);
      }
      
      const firstVersion = versions.results[0];
      const product = {
        id: firstVersion.product_id,
        name: firstVersion.product_name,
        description: firstVersion.product_description,
        category: firstVersion.category,
        versions: versions.results.map((item: any) => ({
          id: item.version_id,
          version: item.version,
          version_name: item.version_name,
          description: item.description,
          features: item.features ? JSON.parse(item.features) : [],
          changelog: item.changelog,
          price: item.custom_price || item.default_price,
          original_price: item.default_price,
        })),
      };
      
      return response.success(c, '商品详情获取成功', {
        distributor: {
          username: distributor.username,
          display_name: distributor.display_name,
          contact_wechat: distributor.contact_wechat,
          avatar_url: distributor.avatar_url,
        },
        product,
      });
    } catch (error) {
      console.error('Get store product error:', error);
      return response.error(c, '服务器内部错误', 500);
    }
  });
}