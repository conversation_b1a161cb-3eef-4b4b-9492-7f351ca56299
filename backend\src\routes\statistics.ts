import { Hono } from 'hono';
import { CloudflareBindings } from '../types/interfaces';
import { authMiddleware } from '../middleware/auth';
import * as response from '../utils/response';

export function setupStatisticsRoutes(app: Hono<{ Bindings: CloudflareBindings }>) {
  // Get sales statistics
  app.get('/api/stats/sales', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const query = c.req.query();
      const period = query.period || 'month';
      const start_date = query.start_date;
      const end_date = query.end_date;
      
      // Build date filter
      let dateFilter = '';
      const params: any[] = [];
      
      if (start_date && end_date) {
        dateFilter = 'AND o.created_at BETWEEN ? AND ?';
        params.push(start_date, end_date);
      } else {
        // Default period-based filtering
        const now = new Date();
        let startDate: Date;
        
        switch (period) {
          case 'day':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          case 'year':
            startDate = new Date(now.getFullYear(), 0, 1);
            break;
          default:
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }
        
        dateFilter = 'AND o.created_at >= ?';
        params.push(startDate.toISOString());
      }
      
      // Check cache first
      const cacheKey = `stats:${user.user_id}:${period}:${start_date || ''}:${end_date || ''}`;
      const cached = await c.env.CACHE.get(cacheKey);
      
      if (cached) {
        return response.success(c, '销售统计获取成功(缓存)', JSON.parse(cached));
      }
      
      // Base query conditions
      let adminFilter = '';
      if (user.role !== 'admin') {
        adminFilter = 'WHERE o.admin_id = ?';
        params.unshift(user.user_id);
      }
      
      // Get total sales
      const totalSalesQuery = `
        SELECT 
          COUNT(*) as total_orders,
          SUM(license_count) as total_licenses,
          SUM(total_price) as total_revenue
        FROM orders o
        ${adminFilter}
        ${adminFilter ? 'AND' : 'WHERE'} o.status = 'completed'
        ${dateFilter}
      `;
      
      const totalSales = await c.env.DB.prepare(totalSalesQuery).bind(...params).first();
      
      // Get sales by product
      const productSalesQuery = `
        SELECT 
          p.name as product_name,
          COUNT(*) as orders,
          SUM(o.license_count) as licenses,
          SUM(o.total_price) as revenue
        FROM orders o
        JOIN product_versions pv ON o.version_id = pv.id
        JOIN products p ON pv.product_id = p.id
        ${adminFilter}
        ${adminFilter ? 'AND' : 'WHERE'} o.status = 'completed'
        ${dateFilter}
        GROUP BY p.id, p.name
        ORDER BY revenue DESC
      `;
      
      const productSales = await c.env.DB.prepare(productSalesQuery).bind(...params).all();
      
      // Get sales trend (last 30 days)
      const trendQuery = `
        SELECT 
          DATE(o.created_at) as date,
          COUNT(*) as orders,
          SUM(o.license_count) as licenses,
          SUM(o.total_price) as revenue
        FROM orders o
        ${adminFilter}
        ${adminFilter ? 'AND' : 'WHERE'} o.status = 'completed'
        AND o.created_at >= date('now', '-30 days')
        GROUP BY DATE(o.created_at)
        ORDER BY date DESC
      `;
      
      const trendParams = user.role === 'admin' ? [] : [user.user_id];
      const salesTrend = await c.env.DB.prepare(trendQuery).bind(...trendParams).all();
      
      const stats = {
        summary: {
          total_orders: (totalSales?.total_orders as number) || 0,
          total_licenses: (totalSales?.total_licenses as number) || 0,
          total_revenue: (totalSales?.total_revenue as number) || 0,
          period,
        },
        by_product: productSales.results,
        trend: salesTrend.results,
      };
      
      // Cache for 10 minutes
      await c.env.CACHE.put(cacheKey, JSON.stringify(stats), { expirationTtl: 600 });
      
      return response.success(c, '销售统计获取成功', stats);
    } catch (error) {
      console.error('Get sales stats error:', error);
      return response.error(c, 'Internal server error', 500);
    }
  });

  // Get verification statistics
  app.get('/api/stats/verification', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      const query = c.req.query();
      const period = query.period || 'month';
      const start_date = query.start_date;
      const end_date = query.end_date;
      
      // Build date filter
      let dateFilter = '';
      const params: any[] = [];
      
      if (start_date && end_date) {
        dateFilter = 'AND vl.created_at BETWEEN ? AND ?';
        params.push(start_date, end_date);
      } else {
        // Default period-based filtering
        const now = new Date();
        let startDate: Date;
        
        switch (period) {
          case 'day':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          case 'year':
            startDate = new Date(now.getFullYear(), 0, 1);
            break;
          default:
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }
        
        dateFilter = 'AND vl.created_at >= ?';
        params.push(startDate.toISOString());
      }
      
      // Base query - filter by user's products for distributors
      let productFilter = '';
      if (user.role !== 'admin') {
        const userInfo = await c.env.CACHE.get(`user:auth:${user.user_id}`);
        if (userInfo) {
          const { product_ids } = JSON.parse(userInfo);
          if (product_ids && product_ids.length > 0) {
            productFilter = `AND pv.product_id IN (${product_ids.map(() => '?').join(',')})`;
            params.push(...product_ids);
          } else {
            // User has no assigned products
            return response.success(c, '无验证数据可用', {
              summary: { total_verifications: 0, successful_verifications: 0, failed_verifications: 0, success_rate: 0 },
              by_result: [],
              trend: [],
            });
          }
        }
      }
      
      // Get total verifications
      const totalQuery = `
        SELECT 
          COUNT(*) as total_verifications,
          SUM(CASE WHEN vl.result = 'success' THEN 1 ELSE 0 END) as successful_verifications,
          SUM(CASE WHEN vl.result = 'failed' THEN 1 ELSE 0 END) as failed_verifications
        FROM verification_logs vl
        JOIN licenses l ON vl.license_key = l.license_key
        JOIN product_versions pv ON l.version_id = pv.id
        WHERE 1=1
        ${productFilter}
        ${dateFilter}
      `;
      
      const totalStats = await c.env.DB.prepare(totalQuery).bind(...params).first();
      
      // Get verification by result
      const resultQuery = `
        SELECT 
          vl.result,
          COUNT(*) as count
        FROM verification_logs vl
        JOIN licenses l ON vl.license_key = l.license_key
        JOIN product_versions pv ON l.version_id = pv.id
        WHERE 1=1
        ${productFilter}
        ${dateFilter}
        GROUP BY vl.result
      `;
      
      const resultStats = await c.env.DB.prepare(resultQuery).bind(...params).all();
      
      // Get verification trend (last 30 days)
      const trendQuery = `
        SELECT 
          DATE(vl.created_at) as date,
          COUNT(*) as total_verifications,
          SUM(CASE WHEN vl.result = 'success' THEN 1 ELSE 0 END) as successful_verifications
        FROM verification_logs vl
        JOIN licenses l ON vl.license_key = l.license_key
        JOIN product_versions pv ON l.version_id = pv.id
        WHERE vl.created_at >= date('now', '-30 days')
        ${productFilter ? productFilter.replace('AND', 'AND') : ''}
        GROUP BY DATE(vl.created_at)
        ORDER BY date DESC
      `;
      
      const trendParams = user.role === 'admin' ? [] : params.slice(1);
      const verificationTrend = await c.env.DB.prepare(trendQuery).bind(...trendParams).all();
      
      const successfulVerifications = (totalStats?.successful_verifications as number) || 0;
      const totalVerifications = (totalStats?.total_verifications as number) || 0;
      const successRate = totalVerifications > 0 ? (successfulVerifications / totalVerifications) * 100 : 0;
      
      const stats = {
        summary: {
          total_verifications: totalVerifications,
          successful_verifications: successfulVerifications,
          failed_verifications: (totalStats?.failed_verifications as number) || 0,
          success_rate: Math.round(successRate * 100) / 100,
          period,
        },
        by_result: resultStats.results,
        trend: verificationTrend.results,
      };
      
      return response.success(c, '验证统计获取成功', stats);
    } catch (error) {
      console.error('Get verification stats error:', error);
      return response.error(c, 'Internal server error', 500);
    }
  });

  // Get dashboard overview
  app.get('/api/stats/dashboard', authMiddleware(), async (c) => {
    try {
      const user = c.get('user');
      
      // Base conditions for distributors
      let productFilter = '';
      let adminFilter = '';
      const params: any[] = [];
      
      if (user.role !== 'admin') {
        adminFilter = 'WHERE admin_id = ?';
        params.push(user.user_id);
        
        const userInfo = await c.env.CACHE.get(`user:auth:${user.user_id}`);
        if (userInfo) {
          const { product_ids } = JSON.parse(userInfo);
          if (product_ids && product_ids.length > 0) {
            productFilter = `AND pv.product_id IN (${product_ids.map(() => '?').join(',')})`;
            params.push(...product_ids);
          }
        }
      }
      
      // Get license counts
      const licenseQuery = `
        SELECT 
          COUNT(*) as total_licenses,
          SUM(CASE WHEN l.status = 'active' THEN 1 ELSE 0 END) as active_licenses,
          SUM(CASE WHEN l.status = 'expired' THEN 1 ELSE 0 END) as expired_licenses,
          SUM(CASE WHEN l.status = 'revoked' THEN 1 ELSE 0 END) as revoked_licenses
        FROM licenses l
        ${user.role !== 'admin' ? 'JOIN product_versions pv ON l.version_id = pv.id' : ''}
        ${adminFilter.replace('admin_id', 'l.admin_id')}
        ${user.role !== 'admin' && productFilter ? productFilter.replace('product_id', 'pv.product_id') : ''}
      `;
      
      const licenseStats = await c.env.DB.prepare(licenseQuery).bind(...params).first();
      
      // Get order counts (this month)
      const orderQuery = `
        SELECT 
          COUNT(*) as total_orders,
          SUM(total_price) as total_revenue,
          SUM(license_count) as total_licenses_sold
        FROM orders
        ${adminFilter}
        ${adminFilter ? 'AND' : 'WHERE'} status = 'completed'
        AND created_at >= date('now', 'start of month')
      `;
      
      const orderStats = await c.env.DB.prepare(orderQuery).bind(...params.slice(0, user.role === 'admin' ? 0 : 1)).first();
      
      // Get verification counts (today)
      const verificationQuery = `
        SELECT 
          COUNT(*) as total_verifications,
          SUM(CASE WHEN vl.result = 'success' THEN 1 ELSE 0 END) as successful_verifications
        FROM verification_logs vl
        ${user.role === 'admin' ? '' : 'JOIN licenses l ON vl.license_key = l.license_key JOIN product_versions pv ON l.version_id = pv.id'}
        WHERE vl.created_at >= date('now')
        ${user.role === 'admin' ? '' : productFilter.replace('AND ', 'AND ')}
      `;
      
      const verificationParams = user.role === 'admin' ? [] : (productFilter ? params.slice(1) : []);
      const verificationStats = await c.env.DB.prepare(verificationQuery).bind(...verificationParams).first();
      
      // Get active devices count
      const deviceQuery = `
        SELECT COUNT(DISTINCT device_id) as active_devices
        FROM devices d
        JOIN licenses l ON d.license_id = l.id
        ${user.role !== 'admin' ? 'JOIN product_versions pv ON l.version_id = pv.id' : ''}
        WHERE d.last_verification >= date('now', '-7 days')
        ${user.role !== 'admin' && productFilter ? 'AND ' + productFilter.replace('product_id', 'pv.product_id') : ''}
      `;
      
      const deviceParams = user.role === 'admin' ? [] : params.slice(1);
      const deviceStats = await c.env.DB.prepare(deviceQuery).bind(...deviceParams).first();
      
      const dashboardData = {
        licenses: {
          total: (licenseStats?.total_licenses as number) || 0,
          active: (licenseStats?.active_licenses as number) || 0,
          expired: (licenseStats?.expired_licenses as number) || 0,
          revoked: (licenseStats?.revoked_licenses as number) || 0,
        },
        orders_this_month: {
          total: (orderStats?.total_orders as number) || 0,
          revenue: (orderStats?.total_revenue as number) || 0,
          licenses_sold: (orderStats?.total_licenses_sold as number) || 0,
        },
        verifications_today: {
          total: (verificationStats?.total_verifications as number) || 0,
          successful: (verificationStats?.successful_verifications as number) || 0,
        },
        active_devices_week: (deviceStats?.active_devices as number) || 0,
      };
      
      return response.success(c, '仪表板统计获取成功', dashboardData);
    } catch (error) {
      console.error('Get dashboard stats error:', error);
      return response.error(c, 'Internal server error', 500);
    }
  });
}