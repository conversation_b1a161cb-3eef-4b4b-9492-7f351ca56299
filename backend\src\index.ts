import { Hono } from 'hono';
import { CloudflareBindings } from './types/interfaces';
import { corsMiddleware, bindingsMiddleware } from './middleware/common';
import { setupAuthRoutes } from './routes/auth';
import { setupUserRoutes } from './routes/user';
import { setupLicenseRoutes } from './routes/licenses';
import { setupVerificationRoutes } from './routes/verification';
import { setupActivationRoutes } from './routes/activation';
import { setupOrderRoutes } from './routes/orders';
import { setupProductRoutes } from './routes/products';
import { setupVersionRoutes } from './routes/versions';
import { setupAuthorizationRoutes } from './routes/authorizations';
import { setupStoreRoutes } from './routes/store';
import { setupStatisticsRoutes } from './routes/statistics';
import { setupInitRoutes } from './routes/init';

const app = new Hono<{ Bindings: CloudflareBindings }>();

// 全局中间件
app.use('*', corsMiddleware());
app.use('*', bindingsMiddleware());

// 健康检查
app.get('/', (c) => {
  return c.json({
    success: true,
    message: 'License Verification Service API',
    version: c.env.API_VERSION || 'v1',
    timestamp: new Date().toISOString(),
  });
});

// 设置路由
setupAuthRoutes(app);
setupUserRoutes(app);
setupLicenseRoutes(app);
setupVerificationRoutes(app);
setupActivationRoutes(app);
setupOrderRoutes(app);
setupProductRoutes(app);
setupVersionRoutes(app);
setupAuthorizationRoutes(app);
setupStoreRoutes(app);
setupStatisticsRoutes(app);
setupInitRoutes(app);

export default app;