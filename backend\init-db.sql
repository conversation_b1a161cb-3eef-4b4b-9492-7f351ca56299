-- 许可证验证系统数据库初始化脚本
-- 创建时间：2025-07-31

-- 1. 用户表（包含管理员和分发商）
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('admin', 'distributor')),
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    -- 分发商基本信息
    display_name TEXT, -- 显示名称，如 "小明软件店"
    contact_wechat TEXT, -- 微信号，用于客户联系
    avatar_url TEXT, -- 头像URL链接
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 2. 产品表（基础信息）
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    category TEXT,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 3. 产品版本表
CREATE TABLE IF NOT EXISTS product_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    version TEXT NOT NULL, -- 如 "1.0.0", "2.1.0"
    version_name TEXT, -- 如 "基础版", "专业版"
    description TEXT,
    features TEXT, -- JSON格式的功能列表
    verification_strategy TEXT NOT NULL DEFAULT 'device_count' CHECK (verification_strategy IN ('device_count', 'expiration', 'feature_based')),
    max_devices INTEGER DEFAULT 1,
    default_price DECIMAL(10,2) NOT NULL,
    download_link TEXT,
    changelog TEXT, -- 版本更新说明
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    UNIQUE(product_id, version)
);

-- 4. 分发商授权表（店铺商品授权）
CREATE TABLE IF NOT EXISTS distributor_authorizations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    distributor_id INTEGER NOT NULL,
    version_id INTEGER NOT NULL,
    -- 分发商店铺定价
    custom_price DECIMAL(10,2), -- 分发商设置的价格，如果为NULL则使用默认价格
    -- 状态管理
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (distributor_id) REFERENCES users(id),
    FOREIGN KEY (version_id) REFERENCES product_versions(id),
    UNIQUE(distributor_id, version_id)
);

-- 5. 许可证表（更新为使用版本ID，关联订单）
CREATE TABLE IF NOT EXISTS licenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version_id INTEGER NOT NULL,
    license_key TEXT NOT NULL UNIQUE,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'revoked')),
    expires_at DATETIME,
    max_devices INTEGER,
    features TEXT, -- JSON 数组，覆盖版本默认功能
    admin_id INTEGER NOT NULL,
    order_id INTEGER, -- 关联订单ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (version_id) REFERENCES product_versions(id),
    FOREIGN KEY (admin_id) REFERENCES users(id),
    FOREIGN KEY (order_id) REFERENCES orders(id)
);

-- 6. 设备绑定表
CREATE TABLE IF NOT EXISTS devices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_id INTEGER NOT NULL,
    device_id TEXT NOT NULL,
    device_info TEXT, -- JSON 格式，存储设备信息
    last_verification DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
    UNIQUE(license_id, device_id)
);

-- 7. 验证日志表
CREATE TABLE IF NOT EXISTS verification_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT NOT NULL,
    device_id TEXT,
    result TEXT NOT NULL CHECK (result IN ('success', 'failed')),
    reason TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 8. 订单表（更新为使用版本ID和支持分发商定价，关联许可证）
CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    admin_id INTEGER NOT NULL,
    version_id INTEGER NOT NULL,
    license_count INTEGER NOT NULL DEFAULT 1, -- 通常为1，表示单个许可证
    unit_price DECIMAL(10,2) NOT NULL, -- 实际销售价格
    total_price DECIMAL(10,2) NOT NULL,
    status TEXT NOT NULL DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled')), -- 生成许可证时直接完成
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES users(id),
    FOREIGN KEY (version_id) REFERENCES product_versions(id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_licenses_key ON licenses(license_key);
CREATE INDEX IF NOT EXISTS idx_licenses_version ON licenses(version_id);
CREATE INDEX IF NOT EXISTS idx_licenses_admin ON licenses(admin_id);
CREATE INDEX IF NOT EXISTS idx_licenses_order ON licenses(order_id);
CREATE INDEX IF NOT EXISTS idx_devices_license ON devices(license_id);
CREATE INDEX IF NOT EXISTS idx_verification_logs_key ON verification_logs(license_key);
CREATE INDEX IF NOT EXISTS idx_verification_logs_created ON verification_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_admin ON orders(admin_id);
CREATE INDEX IF NOT EXISTS idx_orders_version ON orders(version_id);
CREATE INDEX IF NOT EXISTS idx_orders_created ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_product_versions_product ON product_versions(product_id);
CREATE INDEX IF NOT EXISTS idx_distributor_auth_distributor ON distributor_authorizations(distributor_id);
CREATE INDEX IF NOT EXISTS idx_distributor_auth_version ON distributor_authorizations(version_id);

-- 插入示例数据

-- 插入产品数据
INSERT OR IGNORE INTO products (id, name, description, category) VALUES
(1, 'MyAwesomeApp', '我开发的超棒软件', 'productivity'),
(2, 'GameChanger', '游戏辅助工具', 'gaming');

-- 插入产品版本数据
INSERT OR IGNORE INTO product_versions (id, product_id, version, version_name, description, features, verification_strategy, max_devices, default_price, download_link) VALUES
(1, 1, '1.0.0', '基础版', '提供基本功能', '["basic_features", "email_support"]', 'device_count', 1, 99.00, 'https://example.com/download/v1.0.0'),
(2, 1, '2.0.0', '专业版', '提供高级功能', '["basic_features", "advanced_features", "priority_support"]', 'device_count', 3, 299.00, 'https://example.com/download/v2.0.0'),
(3, 2, '1.0.0', '标准版', '游戏辅助基础功能', '["game_helper", "basic_automation"]', 'device_count', 1, 199.00, 'https://example.com/game/v1.0.0');

-- 插入用户账户（需要在应用启动后通过代码插入，因为需要密码哈希）
-- 管理员：admin / password （完整权限）
-- 分发商：xiaoming / password (显示名称: "小明的软件店", 微信: xiaoming123)

-- 示例授权数据（分发商xiaoming被授权销售产品版本1和2）
-- INSERT INTO distributor_authorizations (distributor_id, version_id, custom_price) VALUES
-- (2, 1, 109.00), -- 小明店铺的基础版定价 ¥109
-- (2, 2, 329.00); -- 小明店铺的专业版定价 ¥329